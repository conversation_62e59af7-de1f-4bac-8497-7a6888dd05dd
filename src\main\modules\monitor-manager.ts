import * as chokidar from 'chokidar';
import * as cron from 'node-cron';
import * as path from 'path';
import * as fs from 'fs-extra';
import { EventEmitter } from 'events';
import {
  MonitorTask,
  MonitorEvent,
  MonitorExecutionResult,
  MonitorTaskStatus,
  MonitorErrorRecord,
  FileWatchConfig,
  ScheduledConfig,
  AppFile,
  Workflow
} from '../../shared/types';
import { WorkflowEngine } from './workflow-engine';
import { HistoryManager } from './history-manager';
import { v4 as uuidv4 } from 'uuid';

export class MonitorManager extends EventEmitter {
  private tasks: Map<string, MonitorTask> = new Map();
  private watchers: Map<string, chokidar.FSWatcher> = new Map();
  private cronJobs: Map<string, cron.ScheduledTask> = new Map();
  private runningExecutions: Map<string, string> = new Map(); // taskId -> executionId
  private fileQueues: Map<string, string[]> = new Map(); // taskId -> pending files
  private watcherCleanups: Map<string, () => void> = new Map(); // taskId -> cleanup function
  private workflowEngine: WorkflowEngine;
  private historyManager: HistoryManager;
  private store: any; // electron-store instance
  private getWorkflowFn: (workflowId: string) => Promise<Workflow | null>;

  constructor(workflowEngine: WorkflowEngine, historyManager: HistoryManager, store: any, getWorkflowFn: (workflowId: string) => Promise<Workflow | null>) {
    super();
    this.workflowEngine = workflowEngine;
    this.historyManager = historyManager;
    this.store = store;
    this.getWorkflowFn = getWorkflowFn;
  }

  /**
   * 初始化监控管理器
   */
  async initialize(): Promise<void> {
    await this.loadTasks();
  }

  /**
   * 加载所有监控任务
   */
  private async loadTasks(): Promise<void> {
    try {
      const savedTasks = this.store.get('monitorTasks', []) as MonitorTask[];
      for (const task of savedTasks) {
        this.tasks.set(task.id, task);
        if (task.enabled) {
          await this.startTask(task.id);
        }
      }
      console.log(`已加载 ${savedTasks.length} 个监控任务`);
    } catch (error) {
      console.error('加载监控任务失败:', error);
    }
  }

  /**
   * 保存所有监控任务
   */
  private async saveTasks(): Promise<void> {
    try {
      const tasksArray = Array.from(this.tasks.values());
      console.log(`[保存任务] 保存${tasksArray.length}个监控任务到存储`);
      this.store.set('monitorTasks', tasksArray);
      console.log(`[保存任务] 任务保存成功`);
    } catch (error) {
      console.error('[保存任务] 保存监控任务失败:', error);
      throw error; // 重新抛出错误，让调用者知道保存失败
    }
  }

  /**
   * 创建新的监控任务
   */
  async createTask(taskData: Omit<MonitorTask, 'id' | 'createdAt' | 'updatedAt' | 'statistics'>): Promise<MonitorTask> {
    const task: MonitorTask = {
      ...taskData,
      id: uuidv4(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      statistics: {
        totalExecutions: 0,
        successfulExecutions: 0,
        failedExecutions: 0,
        totalFilesProcessed: 0
      }
    };

    this.tasks.set(task.id, task);
    await this.saveTasks();

    if (task.enabled) {
      await this.startTask(task.id);
    }

    this.emit('taskCreated', task);
    return task;
  }

  /**
   * 更新监控任务
   */
  async updateTask(taskId: string, updates: Partial<MonitorTask>): Promise<MonitorTask | null> {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error(`监控任务不存在: ${taskId}`);
    }

    const wasEnabled = task.enabled;
    const updatedTask = {
      ...task,
      ...updates,
      id: taskId, // 确保ID不被修改
      updatedAt: new Date().toISOString()
    };

    console.log(`[监控任务] 更新任务 ${task.name}: ${wasEnabled} -> ${updatedTask.enabled}`);

    // 处理启用/禁用状态变化
    if (wasEnabled && !updatedTask.enabled) {
      // 禁用任务：先更新任务数据，然后停止任务
      console.log(`[更新任务] 禁用任务: ${task.name}`);
      this.tasks.set(taskId, updatedTask);
      await this.saveTasks();
      await this.stopTask(taskId);
      // 获取stopTask更新后的最新任务状态
      const finalTask = this.tasks.get(taskId) || updatedTask;
      console.log(`[更新任务] 禁用完成，最终状态:`, finalTask.enabled, finalTask.status);
      this.emit('taskUpdated', finalTask);
      return finalTask;
    } else if (!wasEnabled && updatedTask.enabled) {
      // 启用任务：先更新任务数据，然后启动
      this.tasks.set(taskId, updatedTask);
      await this.saveTasks();
      await this.startTask(taskId);
      // 获取startTask更新后的最新状态
      const finalTask = this.tasks.get(taskId) || updatedTask;
      this.emit('taskUpdated', finalTask);
      return finalTask;
    } else if (updatedTask.enabled) {
      // 如果任务仍然启用，重启以应用新配置
      this.tasks.set(taskId, updatedTask);
      await this.saveTasks();
      await this.stopTask(taskId);
      await this.startTask(taskId);
      // 获取重启后的最新状态
      const finalTask = this.tasks.get(taskId) || updatedTask;
      this.emit('taskUpdated', finalTask);
      return finalTask;
    } else {
      // 任务保持禁用状态，只更新数据
      this.tasks.set(taskId, updatedTask);
      await this.saveTasks();
      this.emit('taskUpdated', updatedTask);
      return updatedTask;
    }
  }

  /**
   * 删除监控任务
   */
  async deleteTask(taskId: string): Promise<boolean> {
    console.log(`[删除任务] 开始删除任务: ${taskId}`);

    const task = this.tasks.get(taskId);
    if (!task) {
      console.log(`[删除任务] 任务不存在: ${taskId}`);
      return false;
    }

    console.log(`[删除任务] 停止任务: ${task.name}`);
    await this.stopTask(taskId);

    console.log(`[删除任务] 从内存中删除任务`);
    this.tasks.delete(taskId);
    await this.saveTasks();

    console.log(`[删除任务] 发送删除事件`);
    this.emit('taskDeleted', taskId);

    console.log(`[删除任务] 删除完成: ${taskId}`);
    return true;
  }

  /**
   * 获取所有监控任务
   */
  getAllTasks(): MonitorTask[] {
    return Array.from(this.tasks.values());
  }

  /**
   * 获取单个监控任务
   */
  getTask(taskId: string): MonitorTask | null {
    return this.tasks.get(taskId) || null;
  }

  /**
   * 启动监控任务
   */
  async startTask(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task || !task.enabled) {
      return;
    }

    try {
      if (task.type === 'file_watch') {
        await this.startFileWatcher(task);
      } else if (task.type === 'scheduled') {
        await this.startScheduledTask(task);
      }

      // 更新任务状态
      task.status = 'idle';
      await this.saveTasks();

      console.log(`监控任务已启动: ${task.name}`);
    } catch (error) {
      console.error(`启动监控任务失败: ${task.name}`, error);
      task.status = 'error';
      await this.saveTasks();
    }
  }

  /**
   * 停止监控任务
   */
  async stopTask(taskId: string): Promise<void> {
    console.log(`[停止任务] 开始停止任务: ${taskId}`);

    const task = this.tasks.get(taskId);
    if (!task) {
      console.log(`[停止任务] 任务不存在: ${taskId}`);
      return;
    }

    console.log(`[停止任务] 停止任务: ${task.name}, 当前状态: ${task.status}`);

    // 停止文件监控
    const watcher = this.watchers.get(taskId);
    if (watcher) {
      console.log(`[停止任务] 关闭文件监控器`);

      // 先调用清理函数
      const cleanup = this.watcherCleanups.get(taskId);
      if (cleanup) {
        console.log(`[停止任务] 执行清理函数`);
        cleanup();
        this.watcherCleanups.delete(taskId);
      }

      // 然后关闭监控器
      await watcher.close();
      this.watchers.delete(taskId);
    }

    // 停止定时任务
    const cronJob = this.cronJobs.get(taskId);
    if (cronJob) {
      console.log(`[停止任务] 停止定时任务`);
      cronJob.stop();
      cronJob.destroy();
      this.cronJobs.delete(taskId);
    }

    // 清理执行队列和状态
    console.log(`[停止任务] 清理执行队列和状态`);
    this.fileQueues.delete(taskId);
    this.runningExecutions.delete(taskId);

    // 更新任务状态
    task.status = 'disabled';
    await this.saveTasks();

    console.log(`[停止任务] 监控任务已停止: ${task.name}, enabled: ${task.enabled}, status: ${task.status}`);
  }

  /**
   * 启动文件监控
   */
  private async startFileWatcher(task: MonitorTask): Promise<void> {
    const config = task.config as FileWatchConfig;
    
    // 获取工作流以确定是否包含子文件夹
    const workflow = await this.getWorkflow(task.workflowId);
    const includeSubfolders = workflow?.includeSubfolders !== false; // 默认为true

    const watcher = chokidar.watch(config.watchPaths, {
      ignored: config.ignorePatterns || [],
      persistent: true,
      ignoreInitial: true,
      depth: includeSubfolders ? undefined : 0, // 控制递归深度
      awaitWriteFinish: {
        stabilityThreshold: config.debounceMs,
        pollInterval: 100
      }
    });

    // 文件变化处理队列
    const changeQueue: { path: string; event: string; stats?: any }[] = [];
    let processTimeout: NodeJS.Timeout | null = null;

    const processChanges = async () => {
      if (changeQueue.length === 0) return;

      const changes = [...changeQueue];
      changeQueue.length = 0;

      try {
        await this.handleFileChanges(task, changes);
      } catch (error) {
        console.error(`处理文件变化失败: ${task.name}`, error);
      }
    };

    const queueChange = (event: string, filePath: string, stats?: any) => {
      changeQueue.push({ path: filePath, event, stats });

      if (processTimeout) {
        clearTimeout(processTimeout);
      }

      processTimeout = setTimeout(processChanges, config.batchTimeoutMs || 1000);
    };

    // 清理函数，确保定时器被正确清理
    const cleanup = () => {
      if (processTimeout) {
        clearTimeout(processTimeout);
        processTimeout = null;
      }
      if (watcher) {
        watcher.close();
      }
    };

    // 监听文件事件
    config.events.forEach(eventType => {
      watcher.on(eventType, (filePath: string, stats?: any) => {
        queueChange(eventType, filePath, stats);
      });
    });

    watcher.on('error', (error) => {
      console.error(`文件监控错误: ${task.name}`, error);
      this.emit('taskError', { taskId: task.id, error: error instanceof Error ? error.message : String(error) });
    });

    // 存储监控器和清理函数
    this.watchers.set(task.id, watcher);

    // 存储清理函数，在stopTask时调用
    this.watcherCleanups.set(task.id, cleanup);
  }

  /**
   * 启动定时任务
   */
  private async startScheduledTask(task: MonitorTask): Promise<void> {
    const config = task.config as ScheduledConfig;

    // 验证 cron 表达式
    if (!cron.validate(config.cronExpression)) {
      throw new Error(`无效的 cron 表达式: ${config.cronExpression}`);
    }

    const cronJob = cron.schedule(config.cronExpression, async () => {
      // 检查任务是否仍然启用
      const currentTask = this.tasks.get(task.id);
      if (!currentTask || !currentTask.enabled) {
        console.log(`定时任务已禁用，跳过执行: ${task.name}`);
        return;
      }

      // 检查是否需要跳过正在运行的任务
      if (config.skipIfRunning && currentTask.status === 'running') {
        console.log(`定时任务正在运行，跳过本次执行: ${task.name}`);
        return;
      }

      await this.executeScheduledTask(currentTask);
    }, {
      timezone: config.timezone || 'Asia/Shanghai'
    });

    this.cronJobs.set(task.id, cronJob);

    // 计算下次执行时间
    task.nextExecution = this.getNextExecutionTime(config.cronExpression);
    await this.saveTasks();

    console.log(`定时任务已启动: ${task.name}, 下次执行: ${task.nextExecution}`);
  }

  /**
   * 处理文件变化
   */
  private async handleFileChanges(task: MonitorTask, changes: { path: string; event: string; stats?: any }[]): Promise<void> {
    if (!task.enabled) {
      console.log(`[文件变化] 任务${task.name}已禁用，跳过处理`);
      return;
    }

    const config = task.config as FileWatchConfig;

    // 收集所有变化的文件
    const filePaths = changes
      .filter(change => change.event === 'add' || change.event === 'change')
      .map(change => change.path)
      .filter(filePath => fs.existsSync(filePath) && fs.statSync(filePath).isFile());

    console.log(`[文件变化] 任务${task.name}: 检测到${changes.length}个变化，有效文件${filePaths.length}个`);

    if (filePaths.length === 0) {
      return;
    }

    if (config.autoExecute) {
      const globalBatchSize = this.store.get('workflow.processing.batchSize', 100) as number;
      console.log(`[文件变化] 任务${task.name}: 自动执行已启用，批处理大小: ${globalBatchSize}`);

      // 将文件加入队列
      this.addToFileQueue(task.id, filePaths);

      // 如果任务没有在执行，开始处理队列
      if (!this.runningExecutions.has(task.id)) {
        console.log(`[文件变化] 任务${task.name}: 开始处理队列`);
        await this.processQueuedFiles(task);
      } else {
        console.log(`[文件变化] 任务${task.name}: 正在执行中，文件已加入队列等待`);
      }
    } else {
      console.log(`[文件变化] 任务${task.name}: 自动执行已禁用，发送检测事件`);
      // 发送文件检测事件，让用户决定是否执行
      this.emit('filesDetected', {
        taskId: task.id,
        files: filePaths,
        changes
      });
    }
  }

  /**
   * 添加文件到待处理队列
   */
  private addToFileQueue(taskId: string, filePaths: string[]): void {
    if (!this.fileQueues.has(taskId)) {
      this.fileQueues.set(taskId, []);
    }
    const queue = this.fileQueues.get(taskId)!;
    // 去重并添加新文件
    const newFiles = filePaths.filter(path => !queue.includes(path));
    queue.push(...newFiles);

    console.log(`[队列管理] 任务${taskId}: 新增${newFiles.length}个文件，队列总数: ${queue.length}`);
  }



  /**
   * 处理队列中的文件
   */
  private async processQueuedFiles(task: MonitorTask): Promise<void> {
    console.log(`[队列处理] 任务${task.name}: 开始处理队列`);

    // 检查任务是否启用和是否正在执行
    if (!task.enabled) {
      console.log(`[队列处理] 任务${task.name}: 任务已禁用，停止处理`);
      return;
    }

    if (this.runningExecutions.has(task.id)) {
      console.log(`[队列处理] 任务${task.name}: 任务正在执行中，跳过处理`);
      return;
    }

    const queue = this.fileQueues.get(task.id);
    if (!queue || queue.length === 0) {
      console.log(`[队列处理] 任务${task.name}: 队列为空，无需处理`);
      return;
    }

    const config = task.config as FileWatchConfig;
    // 使用全局设置中的批处理大小
    const batchSize = this.store.get('workflow.processing.batchSize', 100) as number;

    console.log(`[队列处理] 任务${task.name}: 队列中有${queue.length}个文件，批处理大小: ${batchSize}`);

    // 取出一批文件处理
    const batch = queue.splice(0, batchSize);

    // 过滤仍然存在的文件
    const existingFiles = batch.filter(filePath =>
      fs.existsSync(filePath) && fs.statSync(filePath).isFile()
    );

    console.log(`[队列处理] 任务${task.name}: 从队列取出${batch.length}个文件，其中${existingFiles.length}个仍存在，队列剩余: ${queue.length}个`);

    if (existingFiles.length > 0) {
      try {
        console.log(`[队列处理] 任务${task.name}: 开始执行工作流处理${existingFiles.length}个文件`);
        await this.executeTaskWithFiles(task, existingFiles, 'file_change', {
          changes: existingFiles.map(path => ({ path, event: 'add' }))
        });

        console.log(`[队列处理] 任务${task.name}: 完成处理${existingFiles.length}个文件`);
      } catch (error) {
        console.error(`[队列处理] 任务${task.name}: 处理失败`, error);
        // 即使失败也继续处理剩余文件
      }
    } else {
      console.log(`[队列处理] 任务${task.name}: 没有有效文件需要处理`);
    }

    // 注意：不在这里递归调用，而是在 executeTaskWithFiles 的 finally 块中调用
    // 这样确保每次只处理一批，避免并发问题
  }

  /**
   * 执行定时任务
   */
  private async executeScheduledTask(task: MonitorTask): Promise<void> {
    if (!task.enabled || this.runningExecutions.has(task.id)) {
      return;
    }

    const config = task.config as ScheduledConfig;

    try {
      // 获取所有监控路径（支持多路径）
      const inputPaths = config.inputPaths || [config.inputPath];
      const allFiles: string[] = [];

      // 获取工作流以确定是否包含子文件夹
      const workflow = await this.getWorkflow(task.workflowId);
      const includeSubfolders = workflow?.includeSubfolders !== false; // 默认为true

      // 按顺序处理每个路径
      for (const inputPath of inputPaths) {
        if (inputPath && inputPath.trim()) {
          console.log(`[定时任务] ${task.name}: 收集路径 ${inputPath} 下的文件`);
          const pathFiles = await this.collectFilesFromPath(inputPath, includeSubfolders, undefined, config.ignorePatterns);
          allFiles.push(...pathFiles);
          console.log(`[定时任务] ${task.name}: 路径 ${inputPath} 找到 ${pathFiles.length} 个文件`);
        }
      }

      if (allFiles.length === 0) {
        console.log(`[定时任务] ${task.name}: 所有路径均未找到文件`);
        return;
      }

      // 根据监控事件过滤文件
      const filteredFiles = await this.filterFilesByEvents(allFiles, config.events);
      if (filteredFiles.length === 0) {
        console.log(`[定时任务] ${task.name}: 根据监控事件过滤后无文件需要处理`);
        return;
      }

      console.log(`[定时任务] ${task.name}: 事件过滤后剩余 ${filteredFiles.length} 个文件需要处理`);

      console.log(`[定时任务] ${task.name}: 开始按顺序处理 ${filteredFiles.length} 个文件`);
      await this.executeTaskWithFiles(task, filteredFiles, 'scheduled');
    } catch (error) {
      console.error(`执行定时任务失败: ${task.name}`, error);
    }
  }

  /**
   * 从路径收集文件
   */
  private async collectFilesFromPath(inputPath: string, recursive: boolean, maxFiles?: number, ignorePatterns?: string[]): Promise<string[]> {
    const files: string[] = [];

    if (!fs.existsSync(inputPath)) {
      return files;
    }

    const stat = await fs.stat(inputPath);
    if (stat.isFile()) {
      // 检查文件是否匹配忽略模式
      if (this.shouldIgnoreFile(inputPath, ignorePatterns)) {
        return [];
      }
      return [inputPath];
    }

    if (stat.isDirectory()) {
      const entries = await fs.readdir(inputPath);

      for (const entry of entries) {
        if (maxFiles && files.length >= maxFiles) {
          break;
        }

        const fullPath = path.join(inputPath, entry);

        // 检查文件/文件夹是否匹配忽略模式
        if (this.shouldIgnoreFile(fullPath, ignorePatterns)) {
          continue;
        }

        const entryStat = await fs.stat(fullPath);

        if (entryStat.isFile()) {
          files.push(fullPath);
        } else if (entryStat.isDirectory() && recursive) {
          const subFiles = await this.collectFilesFromPath(fullPath, recursive, maxFiles ? maxFiles - files.length : undefined, ignorePatterns);
          files.push(...subFiles);
        }
      }
    }

    return files;
  }

  /**
   * 检查文件是否应该被忽略
   */
  private shouldIgnoreFile(filePath: string, ignorePatterns?: string[]): boolean {
    if (!ignorePatterns || ignorePatterns.length === 0) {
      return false;
    }

    const fileName = path.basename(filePath);
    const relativePath = filePath;

    for (const pattern of ignorePatterns) {
      if (!pattern.trim()) continue;

      // 简单的通配符匹配
      if (this.matchPattern(fileName, pattern) || this.matchPattern(relativePath, pattern)) {
        return true;
      }
    }

    return false;
  }

  /**
   * 简单的通配符模式匹配
   */
  private matchPattern(text: string, pattern: string): boolean {
    // 转换通配符模式为正则表达式
    const regexPattern = pattern
      .replace(/\./g, '\\.')
      .replace(/\*/g, '.*')
      .replace(/\?/g, '.');

    const regex = new RegExp(`^${regexPattern}$`, 'i');
    return regex.test(text) || regex.test(path.basename(text));
  }

  /**
   * 根据监控事件过滤文件
   * 对于定时任务，我们需要模拟文件事件的概念
   */
  private async filterFilesByEvents(filePaths: string[], events: string[]): Promise<string[]> {
    if (!events || events.length === 0) {
      return filePaths; // 如果没有配置事件，返回所有文件
    }

    const filteredFiles: string[] = [];
    const now = Date.now();
    const recentThreshold = 24 * 60 * 60 * 1000; // 24小时内认为是"最近"的文件

    for (const filePath of filePaths) {
      try {
        if (!fs.existsSync(filePath)) {
          // 文件不存在，跳过
          continue;
        }

        const stats = await fs.stat(filePath);
        const isFile = stats.isFile();
        const isDirectory = stats.isDirectory();

        // 检查文件/文件夹的创建和修改时间
        const createdTime = stats.birthtime.getTime();
        const modifiedTime = stats.mtime.getTime();
        const isRecentlyCreated = (now - createdTime) < recentThreshold;
        const isRecentlyModified = (now - modifiedTime) < recentThreshold && modifiedTime > createdTime;

        let shouldInclude = false;

        // 根据事件类型判断是否包含此文件
        for (const event of events) {
          switch (event) {
            case 'add':
              if (isFile && isRecentlyCreated) {
                shouldInclude = true;
              }
              break;
            case 'change':
              if (isFile && isRecentlyModified) {
                shouldInclude = true;
              }
              break;
            case 'addDir':
              if (isDirectory && isRecentlyCreated) {
                shouldInclude = true;
              }
              break;
            case 'unlink':
            case 'unlinkDir':
              // 对于定时任务，我们无法检测已删除的文件
              // 这些事件类型在定时任务中不适用
              break;
            default:
              // 未知事件类型，包含所有文件
              shouldInclude = true;
              break;
          }

          if (shouldInclude) break;
        }

        if (shouldInclude) {
          filteredFiles.push(filePath);
        }
      } catch (error) {
        console.error(`检查文件状态失败: ${filePath}`, error);
        // 出错时仍然包含该文件
        filteredFiles.push(filePath);
      }
    }

    return filteredFiles;
  }

  /**
   * 执行任务（通用方法）
   */
  private async executeTaskWithFiles(
    task: MonitorTask,
    filePaths: string[],
    triggeredBy: 'file_change' | 'scheduled' | 'manual',
    triggerData?: any
  ): Promise<MonitorExecutionResult> {
    const executionId = uuidv4();
    const startTime = new Date().toISOString();

    this.runningExecutions.set(task.id, executionId);
    task.status = 'running';
    await this.saveTasks();

    try {
      // 获取工作流
      const workflow = await this.getWorkflow(task.workflowId);
      if (!workflow) {
        throw new Error(`工作流不存在: ${task.workflowId}`);
      }

      // 转换文件路径为AppFile对象
      const files: AppFile[] = [];
      for (const filePath of filePaths) {
        try {
          const stat = await fs.stat(filePath);
          const file: AppFile = {
            id: uuidv4(),
            name: path.basename(filePath),
            path: filePath,
            size: stat.size,
            type: this.getFileType(filePath),
            status: 'pending',
            createdDate: stat.birthtime.toISOString()
          };
          files.push(file);
        } catch (error) {
          console.error(`获取文件信息失败: ${filePath}`, error);
        }
      }

      // 执行工作流
      const workflowResult = await this.workflowEngine.execute(files, workflow);
      const endTime = new Date().toISOString();
      const duration = new Date(endTime).getTime() - new Date(startTime).getTime();

      // 更新统计信息
      task.statistics.totalExecutions++;
      task.statistics.totalFilesProcessed += files.length;
      task.statistics.lastExecutionDuration = duration;

      if (workflowResult.errors.length === 0) {
        task.statistics.successfulExecutions++;
        this.recordSuccessfulExecution(task);
      } else {
        task.statistics.failedExecutions++;

        // 记录工作流执行中的错误
        const firstError = workflowResult.errors[0];
        this.recordEnhancedError(
          task,
          firstError?.error || '未知错误',
          executionId,
          triggeredBy,
          files.map(f => f.path),
          firstError?.step
        );
      }

      // 计算平均执行时间
      if (task.statistics.totalExecutions > 0) {
        const avgTime = task.statistics.averageExecutionTime || 0;
        task.statistics.averageExecutionTime =
          (avgTime * (task.statistics.totalExecutions - 1) + duration) / task.statistics.totalExecutions;
      }

      task.lastExecuted = endTime;
      task.status = 'idle';

      // 如果是定时任务，计算下次执行时间
      if (task.type === 'scheduled') {
        const config = task.config as ScheduledConfig;
        task.nextExecution = this.getNextExecutionTime(config.cronExpression);
      }

      await this.saveTasks();

      const result: MonitorExecutionResult = {
        taskId: task.id,
        executionId,
        startTime,
        endTime,
        duration,
        status: workflowResult.errors.length === 0 ? 'success' : (workflowResult.processedFiles > 0 ? 'partial' : 'error'),
        filesProcessed: workflowResult.processedFiles,
        workflowResult,
        triggeredBy,
        triggerData
      };

      // 添加历史记录（所有监控任务执行）
      if (triggeredBy === 'file_change' || triggeredBy === 'scheduled' || triggeredBy === 'manual') {
        try {
          const sourceType = triggeredBy === 'file_change' ? 'file_watch' :
                           triggeredBy === 'scheduled' ? 'scheduled' : 'manual';
          // 获取工作流执行过程中创建的文件夹信息
          const createdDirectories = this.workflowEngine.getCreatedDirectories();
          const cleanedEmptyDirectories = this.workflowEngine.getCleanedEmptyDirectories();
          const historyEntry = this.historyManager.createEntryFromWorkflowResult(
            workflowResult,
            workflow,
            files,
            sourceType,
            task.id,
            task.name,
            createdDirectories,
            cleanedEmptyDirectories
          );
          await this.historyManager.addEntry(historyEntry);
          console.log(`[历史记录] 已添加监控任务执行记录: ${task.name} (${sourceType})`);
        } catch (error) {
          console.error(`[历史记录] 添加监控任务历史记录失败:`, error);
        }
      }

      this.emit('executionCompleted', result);
      return result;

    } catch (error) {
      const endTime = new Date().toISOString();
      const duration = new Date(endTime).getTime() - new Date(startTime).getTime();

      // 更新错误统计
      task.statistics.totalExecutions++;
      task.statistics.failedExecutions++;
      task.status = 'error';

      // 记录增强的错误信息
      this.recordEnhancedError(
        task,
        error,
        executionId,
        triggeredBy,
        filePaths,
        undefined // 没有具体的工作流步骤信息
      );

      await this.saveTasks();

      const result: MonitorExecutionResult = {
        taskId: task.id,
        executionId,
        startTime,
        endTime,
        duration,
        status: 'error',
        filesProcessed: 0,
        workflowResult: {
          workflowId: task.workflowId,
          stepResults: [],
          processedFiles: 0,
          totalFiles: filePaths.length,
          errors: [{ file: '', error: error instanceof Error ? error.message : String(error) }],
          startTime,
          endTime,
          duration
        },
        triggeredBy,
        triggerData
      };

      this.emit('executionFailed', result);
      throw error;
    } finally {
      this.runningExecutions.delete(task.id);
      console.log(`[执行完成] 任务${task.name}: 执行完成，移除运行标记`);

      // 如果是文件监控任务，检查队列中是否还有文件需要处理
      if (task.type === 'file_watch' && triggeredBy === 'file_change') {
        const queue = this.fileQueues.get(task.id);
        if (queue && queue.length > 0 && task.enabled) {
          console.log(`[执行完成] 任务${task.name}: 队列中还有${queue.length}个文件待处理，将在1秒后继续处理`);
          // 延迟一下再处理下一批，避免过于频繁
          setTimeout(() => {
            // 再次检查任务状态，确保任务仍然启用且存在
            const currentTask = this.tasks.get(task.id);
            if (currentTask && currentTask.enabled && !this.runningExecutions.has(task.id)) {
              console.log(`[执行完成] 任务${task.name}: 开始处理下一批文件`);
              this.processQueuedFiles(currentTask).catch(error => {
                console.error(`[执行完成] 任务${task.name}: 处理下一批文件失败`, error);
              });
            } else {
              console.log(`[执行完成] 任务${task.name}: 任务状态已变化，取消队列处理`);
            }
          }, 1000);
        } else {
          console.log(`[执行完成] 任务${task.name}: 队列已清空或任务已禁用，所有文件处理完成`);
        }
      }
    }
  }

  /**
   * 手动执行监控任务
   */
  async executeTask(taskId: string, filePaths?: string[]): Promise<MonitorExecutionResult> {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error(`监控任务不存在: ${taskId}`);
    }

    if (!task.enabled) {
      throw new Error(`监控任务已禁用: ${task.name}`);
    }

    if (this.runningExecutions.has(taskId)) {
      throw new Error(`监控任务正在执行中: ${task.name}`);
    }

    let files: string[] = filePaths || [];

    // 如果没有提供文件路径，根据任务类型收集文件
    if (files.length === 0) {
      // 获取工作流以确定是否包含子文件夹
      const workflow = await this.getWorkflow(task.workflowId);
      const includeSubfolders = workflow?.includeSubfolders !== false; // 默认为true

      if (task.type === 'file_watch') {
        const config = task.config as FileWatchConfig;
        for (const watchPath of config.watchPaths) {
          const pathFiles = await this.collectFilesFromPath(watchPath, includeSubfolders, undefined, config.ignorePatterns);
          files.push(...pathFiles);
        }
      } else if (task.type === 'scheduled') {
        const config = task.config as ScheduledConfig;
        // 支持多路径，按顺序收集文件
        const inputPaths = config.inputPaths || [config.inputPath];
        for (const inputPath of inputPaths) {
          if (inputPath && inputPath.trim()) {
            const pathFiles = await this.collectFilesFromPath(inputPath, includeSubfolders, undefined, config.ignorePatterns);
            files.push(...pathFiles);
          }
        }
      }
    }

    if (files.length === 0) {
      throw new Error(`没有找到要处理的文件`);
    }

    return await this.executeTaskWithFiles(task, files, 'manual');
  }

  /**
   * 获取任务状态
   */
  getTaskStatus(taskId: string): MonitorTaskStatus | null {
    const task = this.tasks.get(taskId);
    if (!task) {
      return null;
    }

    const isRunning = this.runningExecutions.has(taskId);
    const executionId = this.runningExecutions.get(taskId);

    const status: MonitorTaskStatus = {
      taskId,
      isRunning,
      currentExecution: isRunning && executionId ? {
        executionId,
        startTime: new Date().toISOString(), // 这里应该记录实际开始时间
        filesBeingProcessed: 0 // 这里应该记录实际处理的文件数
      } : undefined,
      nextScheduledRun: task.nextExecution,
      lastRun: task.lastExecuted ? {
        timestamp: task.lastExecuted,
        status: task.statistics.lastError ? 'error' : 'success',
        duration: task.statistics.lastExecutionDuration || 0,
        filesProcessed: task.statistics.totalFilesProcessed
      } : undefined
    };

    return status;
  }

  /**
   * 获取所有任务状态
   */
  getAllTaskStatuses(): MonitorTaskStatus[] {
    return Array.from(this.tasks.keys()).map(taskId => this.getTaskStatus(taskId)).filter(Boolean) as MonitorTaskStatus[];
  }

  /**
   * 获取工作流
   */
  private async getWorkflow(workflowId: string): Promise<Workflow | null> {
    return await this.getWorkflowFn(workflowId);
  }

  /**
   * 获取文件类型
   */
  private getFileType(filePath: string): string {
    const ext = path.extname(filePath).toLowerCase();

    const imageExts = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'];
    const videoExts = ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm'];
    const audioExts = ['.mp3', '.wav', '.flac', '.aac', '.ogg', '.wma'];
    const documentExts = ['.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt'];
    const archiveExts = ['.zip', '.rar', '.7z', '.tar', '.gz'];

    if (imageExts.includes(ext)) return 'image';
    if (videoExts.includes(ext)) return 'video';
    if (audioExts.includes(ext)) return 'audio';
    if (documentExts.includes(ext)) return 'document';
    if (archiveExts.includes(ext)) return 'archive';

    return 'other';
  }

  /**
   * 获取下次执行时间
   */
  private getNextExecutionTime(cronExpression: string): string {
    try {
      // 解析 cron 表达式: 分 时 日 月 周
      const parts = cronExpression.trim().split(/\s+/);

      if (parts.length !== 5) {
        throw new Error('Invalid cron expression format, expected 5 parts');
      }

      const [minute, hour, dayOfMonth, month, dayOfWeek] = parts;
      const now = new Date();
      const nextTime = new Date(now);

      // 重置秒和毫秒
      nextTime.setSeconds(0);
      nextTime.setMilliseconds(0);

      // 解析分钟
      let targetMinute = 0;
      if (minute !== '*') {
        targetMinute = parseInt(minute);
        if (isNaN(targetMinute) || targetMinute < 0 || targetMinute > 59) {
          throw new Error('Invalid minute value');
        }
      }

      // 解析小时
      if (hour !== '*' && !hour.includes('/')) {
        // 固定小时
        const targetHour = parseInt(hour);
        if (isNaN(targetHour) || targetHour < 0 || targetHour > 23) {
          throw new Error('Invalid hour value');
        }

        nextTime.setHours(targetHour);
        nextTime.setMinutes(targetMinute);

        // 如果时间已过，移到明天
        if (nextTime <= now) {
          nextTime.setDate(nextTime.getDate() + 1);
        }
      } else if (hour.includes('/')) {
        // 间隔小时，如 */2 表示每2小时
        const match = hour.match(/\*\/(\d+)/);
        if (match) {
          const interval = parseInt(match[1]);
          if (interval > 0 && interval <= 24) {
            const currentHour = now.getHours();
            const currentMinute = now.getMinutes();

            // 计算下一个间隔时间点
            let nextHour = currentHour;

            // 如果当前分钟已过目标分钟，移到下一个小时
            if (currentMinute >= targetMinute) {
              nextHour = currentHour + 1;
            }

            // 找到下一个符合间隔的小时
            nextHour = Math.ceil(nextHour / interval) * interval;

            // 如果超过24小时，移到明天
            if (nextHour >= 24) {
              nextTime.setDate(nextTime.getDate() + 1);
              nextHour = nextHour % 24;
            }

            nextTime.setHours(nextHour);
            nextTime.setMinutes(targetMinute);
          }
        }
      } else {
        // 每小时执行
        nextTime.setMinutes(targetMinute);

        // 如果当前分钟已过目标分钟，移到下一小时
        if (now.getMinutes() >= targetMinute) {
          nextTime.setHours(nextTime.getHours() + 1);
        }
      }

      // 确保下次执行时间在未来
      if (nextTime <= now) {
        nextTime.setTime(nextTime.getTime() + 60000); // 至少1分钟后
      }

      return nextTime.toISOString();
    } catch (error) {
      console.error('计算下次执行时间失败:', error, '表达式:', cronExpression);
      // 如果计算失败，返回一个小时后的时间作为后备
      const fallbackTime = new Date();
      fallbackTime.setHours(fallbackTime.getHours() + 1);
      fallbackTime.setMinutes(0);
      fallbackTime.setSeconds(0);
      fallbackTime.setMilliseconds(0);
      return fallbackTime.toISOString();
    }
  }

  /**
   * 记录增强的错误信息
   */
  private recordEnhancedError(
    task: MonitorTask,
    error: Error | string,
    executionId: string,
    triggeredBy: 'file_change' | 'schedule' | 'manual',
    filesInvolved: string[] = [],
    workflowStep?: string
  ): void {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const stackTrace = error instanceof Error ? error.stack : undefined;

    // 确定错误类型
    let errorType: MonitorErrorRecord['errorType'] = 'system_error';
    if (errorMessage.includes('工作流') || errorMessage.includes('workflow')) {
      errorType = 'workflow_error';
    } else if (errorMessage.includes('文件') || errorMessage.includes('路径') || errorMessage.includes('ENOENT') || errorMessage.includes('EACCES')) {
      errorType = 'file_access_error';
    } else if (errorMessage.includes('配置') || errorMessage.includes('config')) {
      errorType = 'configuration_error';
    }

    // 获取系统信息
    const systemInfo = {
      memoryUsage: process.memoryUsage().heapUsed / 1024 / 1024, // MB
      diskSpace: 0 // 简化实现，实际可以获取磁盘空间
    };

    const errorRecord: MonitorErrorRecord = {
      timestamp: new Date().toISOString(),
      error: errorMessage,
      errorType,
      context: {
        executionId,
        triggeredBy,
        filesInvolved,
        workflowStep,
        systemInfo
      },
      stackTrace
    };

    // 初始化错误历史
    if (!task.statistics.errorHistory) {
      task.statistics.errorHistory = [];
    }

    // 添加错误记录，保持最近50条
    task.statistics.errorHistory.unshift(errorRecord);
    if (task.statistics.errorHistory.length > 50) {
      task.statistics.errorHistory = task.statistics.errorHistory.slice(0, 50);
    }

    // 更新连续失败次数
    task.statistics.consecutiveFailures = (task.statistics.consecutiveFailures || 0) + 1;

    // 更新基本错误信息
    task.statistics.lastError = errorMessage;
    task.statistics.lastErrorTime = errorRecord.timestamp;

    console.error(`[监控错误] 任务${task.name}: ${errorType} - ${errorMessage}`, {
      executionId,
      triggeredBy,
      filesCount: filesInvolved.length,
      consecutiveFailures: task.statistics.consecutiveFailures
    });
  }

  /**
   * 记录成功执行
   */
  private recordSuccessfulExecution(task: MonitorTask): void {
    // 重置连续失败次数
    task.statistics.consecutiveFailures = 0;
    task.statistics.lastSuccessTime = new Date().toISOString();
  }

  /**
   * 获取任务的错误统计摘要
   */
  getTaskErrorSummary(taskId: string): {
    totalErrors: number;
    errorsByType: Record<string, number>;
    recentErrors: MonitorErrorRecord[];
    consecutiveFailures: number;
    lastSuccessTime?: string;
  } | null {
    const task = this.tasks.get(taskId);
    if (!task || !task.statistics.errorHistory) {
      return null;
    }

    const errorHistory = task.statistics.errorHistory;
    const errorsByType: Record<string, number> = {};

    // 统计错误类型
    for (const error of errorHistory) {
      errorsByType[error.errorType] = (errorsByType[error.errorType] || 0) + 1;
    }

    return {
      totalErrors: errorHistory.length,
      errorsByType,
      recentErrors: errorHistory.slice(0, 10), // 最近10个错误
      consecutiveFailures: task.statistics.consecutiveFailures || 0,
      lastSuccessTime: task.statistics.lastSuccessTime
    };
  }

  /**
   * 获取所有任务的错误概览
   */
  getAllTasksErrorOverview(): Array<{
    taskId: string;
    taskName: string;
    status: string;
    consecutiveFailures: number;
    lastError?: string;
    lastErrorTime?: string;
  }> {
    const overview: Array<{
      taskId: string;
      taskName: string;
      status: string;
      consecutiveFailures: number;
      lastError?: string;
      lastErrorTime?: string;
    }> = [];

    for (const [taskId, task] of this.tasks) {
      overview.push({
        taskId,
        taskName: task.name,
        status: task.status,
        consecutiveFailures: task.statistics.consecutiveFailures || 0,
        lastError: task.statistics.lastError,
        lastErrorTime: task.statistics.lastErrorTime
      });
    }

    return overview.sort((a, b) => b.consecutiveFailures - a.consecutiveFailures);
  }

  /**
   * 清理资源
   */
  async cleanup(): Promise<void> {
    console.log('开始清理监控管理器资源...');

    try {
      // 停止所有监控任务
      const taskIds = Array.from(this.tasks.keys());
      console.log(`停止 ${taskIds.length} 个监控任务`);

      for (const taskId of taskIds) {
        try {
          await this.stopTask(taskId);
        } catch (error) {
          console.error(`停止任务 ${taskId} 失败:`, error);
        }
      }

      // 强制清理所有监控器
      console.log(`清理 ${this.watchers.size} 个文件监控器`);
      for (const [taskId, watcher] of this.watchers.entries()) {
        try {
          await watcher.close();
        } catch (error) {
          console.error(`关闭监控器 ${taskId} 失败:`, error);
        }
      }
      this.watchers.clear();

      // 强制清理所有定时任务
      console.log(`清理 ${this.cronJobs.size} 个定时任务`);
      for (const [taskId, cronJob] of this.cronJobs.entries()) {
        try {
          cronJob.stop();
          cronJob.destroy();
        } catch (error) {
          console.error(`清理定时任务 ${taskId} 失败:`, error);
        }
      }
      this.cronJobs.clear();

      // 清理所有队列和执行状态
      this.fileQueues.clear();
      this.runningExecutions.clear();
      this.tasks.clear();

      // 清理所有监听器
      this.removeAllListeners();

      console.log('监控管理器资源清理完成');
    } catch (error) {
      console.error('清理监控管理器资源时出错:', error);
      throw error;
    }
  }
}
