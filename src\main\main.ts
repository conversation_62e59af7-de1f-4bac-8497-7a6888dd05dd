import { app, BrowserWindow, ipcMain, dialog, Menu, Tray, nativeImage } from 'electron';
import path from 'path';
import fs from 'fs-extra';
import { WorkflowEngine } from './modules/workflow-engine';
import { MonitorManager } from './modules/monitor-manager';
import { createDefaultWorkflows } from './modules/default-workflows';
import { validateWorkflow } from './utils/config-validator';
import type { AppFile, Workflow, MonitorTask } from '@shared/types';

const { historyManager } = require('./modules/history-manager');

// 存储实例
let store: any;
let tray: Tray | null = null;
let mainWindow: BrowserWindow | null = null;

// 当前语言设置
let currentLanguage: 'zh-CN' | 'en-US' = 'zh-CN';

// 判断是否为开发环境
const isDev = process.env.NODE_ENV !== 'production' && !app.isPackaged;

// 应用退出状态标记
let isQuitting = false;

// 调试信息（仅开发环境）
if (isDev) {
  console.log('Environment Info:');
  console.log('NODE_ENV:', process.env.NODE_ENV);
  console.log('app.isPackaged:', app.isPackaged);
  console.log('isDev:', isDev);
}

function createWindow() {
  // 设置应用图标路径 - 修复打包后路径问题
  let iconPath: string;

  if (isDev) {
    // 开发环境
    iconPath = path.join(__dirname, '../../resources/logov1.ico');
  } else {
    // 生产环境 - 尝试多个可能的路径
    const possiblePaths = [
      path.join(process.resourcesPath, 'logov1.ico'),
      path.join(process.resourcesPath, 'resources', 'logov1.ico'),
      path.join(__dirname, '../resources/logov1.ico'),
      path.join(__dirname, '../../resources/logov1.ico')
    ];

    iconPath = possiblePaths.find(p => fs.existsSync(p)) || possiblePaths[0];
  }

  console.log('窗口图标路径:', iconPath, '存在:', fs.existsSync(iconPath));

  // 获取主显示器信息
  const { screen } = require('electron');
  const primaryDisplay = screen.getPrimaryDisplay();
  const { width: screenWidth, height: screenHeight } = primaryDisplay.workAreaSize;

  console.log('屏幕工作区域:', { screenWidth, screenHeight });

  // 基于固定屏幕占比计算窗口大小
  // 参考尺寸: 2560x1400屏幕上的1865x994 (72.9% x 71.0%)
  const referenceScreenWidth = 2560;
  const referenceScreenHeight = 1400;
  const referenceWindowWidth = 1865;
  const referenceWindowHeight = 994;

  // 计算参考屏幕的占比
  const widthRatio = referenceWindowWidth / referenceScreenWidth; // ≈ 0.729
  const heightRatio = referenceWindowHeight / referenceScreenHeight; // ≈ 0.710
  const targetAspectRatio = referenceWindowWidth / referenceWindowHeight; // ≈ 1.876

  // 基于当前屏幕大小和参考占比计算窗口尺寸
  let calculatedWidth = Math.floor(screenWidth * widthRatio);
  let calculatedHeight = Math.floor(screenHeight * heightRatio);

  // 验证计算出的尺寸是否保持正确的宽高比，如果不是则调整
  const calculatedAspectRatio = calculatedWidth / calculatedHeight;
  if (Math.abs(calculatedAspectRatio - targetAspectRatio) > 0.01) {
    // 如果宽高比偏差较大，优先保持宽高比，选择较小的尺寸
    const widthBasedHeight = Math.floor(calculatedWidth / targetAspectRatio);
    const heightBasedWidth = Math.floor(calculatedHeight * targetAspectRatio);

    if (widthBasedHeight <= calculatedHeight) {
      calculatedHeight = widthBasedHeight;
    } else {
      calculatedWidth = heightBasedWidth;
    }
  }

  // 确保窗口大小在合理范围内，同时保持宽高比
  const minWidth = 1000;
  const minHeight = Math.floor(minWidth / targetAspectRatio); // ≈ 533
  const maxWidth = 2560;
  const maxHeight = Math.floor(maxWidth / targetAspectRatio); // ≈ 1365

  let windowWidth = Math.max(minWidth, Math.min(calculatedWidth, maxWidth));
  let windowHeight = Math.floor(windowWidth / targetAspectRatio);

  // 确保高度也在合理范围内
  if (windowHeight < minHeight) {
    windowHeight = minHeight;
    windowWidth = Math.floor(windowHeight * targetAspectRatio);
  } else if (windowHeight > maxHeight) {
    windowHeight = maxHeight;
    windowWidth = Math.floor(windowHeight * targetAspectRatio);
  }

  console.log('参考屏幕占比:', {
    widthRatio: (widthRatio * 100).toFixed(1) + '%',
    heightRatio: (heightRatio * 100).toFixed(1) + '%'
  });
  console.log('计算的窗口大小:', { windowWidth, windowHeight });
  console.log('目标宽高比:', targetAspectRatio.toFixed(3));
  console.log('实际宽高比:', (windowWidth / windowHeight).toFixed(3));
  console.log('实际屏幕占比:', {
    widthPercent: (windowWidth / screenWidth * 100).toFixed(1) + '%',
    heightPercent: (windowHeight / screenHeight * 100).toFixed(1) + '%'
  });

  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: windowWidth,
    height: windowHeight,
    minWidth: minWidth,
    minHeight: minHeight,
    frame: false, // 完全隐藏原生窗口边框和标题栏
    titleBarStyle: 'hidden', // 隐藏标题栏
    icon: iconPath, // 设置窗口图标
    webPreferences: {
      // 预加载脚本，这是连接前后端的桥梁
      preload: path.join(__dirname, 'preload.js'),
      contextIsolation: true, // 开启上下文隔离，更安全
      nodeIntegration: false, // 禁止在渲染进程中使用 Node.js
    },
  });

  // 移除默认菜单栏
  Menu.setApplicationMenu(null);

  // 尝试恢复之前保存的窗口状态
  if (store) {
    const savedWindowState = store.get('windowState') as any;
    if (savedWindowState && savedWindowState.width && savedWindowState.height) {
      console.log('恢复保存的窗口状态:', savedWindowState);

      // 验证保存的窗口状态是否在当前屏幕范围内
      if (savedWindowState.x >= 0 && savedWindowState.y >= 0 &&
          savedWindowState.x < screenWidth && savedWindowState.y < screenHeight) {
        mainWindow.setBounds({
          x: savedWindowState.x,
          y: savedWindowState.y,
          width: savedWindowState.width,
          height: savedWindowState.height
        });

        if (savedWindowState.isMaximized) {
          mainWindow.maximize();
        }
      } else {
        // 如果保存的位置超出屏幕范围，则居中显示
        mainWindow.center();
      }
    } else {
      // 没有保存的状态，使用新的默认比例并居中显示
      console.log('没有保存的窗口状态，使用新的默认比例');
      mainWindow.center();
    }
  } else {
    // store 未初始化，居中显示
    mainWindow.center();
  }

  // 监听窗口大小变化，保存用户自定义的窗口大小
  let saveWindowStateTimeout: NodeJS.Timeout;

  const saveWindowState = () => {
    if (mainWindow && !mainWindow.isDestroyed() && store) {
      const bounds = mainWindow.getBounds();
      const isMaximized = mainWindow.isMaximized();

      store.set('windowState', {
        width: bounds.width,
        height: bounds.height,
        x: bounds.x,
        y: bounds.y,
        isMaximized
      });
      console.log('保存窗口状态:', bounds);
    }
  };

  // 防抖保存窗口状态
  const debouncedSaveWindowState = () => {
    clearTimeout(saveWindowStateTimeout);
    saveWindowStateTimeout = setTimeout(saveWindowState, 500);
  };

  // 监听窗口事件
  mainWindow.on('resize', debouncedSaveWindowState);
  mainWindow.on('move', debouncedSaveWindowState);
  mainWindow.on('maximize', saveWindowState);
  mainWindow.on('unmaximize', saveWindowState);



  // 根据环境加载不同的 URL
  // 开发环境加载 Vite 的开发服务器，生产环境加载打包后的 index.html
  const url = isDev
    ? 'http://localhost:5173'
    : `file://${path.join(__dirname, '../renderer/index.html')}`;

  mainWindow.loadURL(url);

  // 如果是开发环境，则打开开发者工具
  if (isDev) {
    mainWindow.webContents.openDevTools();
  }

  // 窗口控制IPC处理
  ipcMain.handle('window:minimize', () => {
    if (mainWindow) {
      mainWindow.minimize();
    }
  });

  ipcMain.handle('window:maximize', () => {
    if (mainWindow) {
      if (mainWindow.isMaximized()) {
        mainWindow.unmaximize();
      } else {
        mainWindow.maximize();
      }
      return mainWindow.isMaximized();
    }
    return false;
  });

  ipcMain.handle('window:close', () => {
    if (mainWindow) {
      const minimizeToTray = store.get('minimizeToTray', false);
      if (minimizeToTray) {
        mainWindow.hide();
        // 更新托盘菜单以反映窗口状态
        updateTrayMenu();
      } else {
        mainWindow.close();
      }
    }
  });

  ipcMain.handle('window:isMaximized', () => {
    return mainWindow ? mainWindow.isMaximized() : false;
  });

  // 重置窗口到默认大小
  ipcMain.handle('window:resetToDefaultSize', () => {
    if (mainWindow) {
      // 获取主显示器信息
      const { screen } = require('electron');
      const primaryDisplay = screen.getPrimaryDisplay();
      const { width: screenWidth, height: screenHeight } = primaryDisplay.workAreaSize;

      // 使用与创建窗口时相同的计算逻辑
      const referenceScreenWidth = 2560;
      const referenceScreenHeight = 1400;
      const referenceWindowWidth = 1865;
      const referenceWindowHeight = 994;

      const widthRatio = referenceWindowWidth / referenceScreenWidth;
      const heightRatio = referenceWindowHeight / referenceScreenHeight;
      const targetAspectRatio = referenceWindowWidth / referenceWindowHeight;

      let calculatedWidth = Math.floor(screenWidth * widthRatio);
      let calculatedHeight = Math.floor(screenHeight * heightRatio);

      const calculatedAspectRatio = calculatedWidth / calculatedHeight;
      if (Math.abs(calculatedAspectRatio - targetAspectRatio) > 0.01) {
        const widthBasedHeight = Math.floor(calculatedWidth / targetAspectRatio);
        const heightBasedWidth = Math.floor(calculatedHeight * targetAspectRatio);

        if (widthBasedHeight <= calculatedHeight) {
          calculatedHeight = widthBasedHeight;
        } else {
          calculatedWidth = heightBasedWidth;
        }
      }

      const minWidth = 1000;
      const minHeight = Math.floor(minWidth / targetAspectRatio);
      const maxWidth = 2560;
      const maxHeight = Math.floor(maxWidth / targetAspectRatio);

      let windowWidth = Math.max(minWidth, Math.min(calculatedWidth, maxWidth));
      let windowHeight = Math.floor(windowWidth / targetAspectRatio);

      if (windowHeight < minHeight) {
        windowHeight = minHeight;
        windowWidth = Math.floor(windowHeight * targetAspectRatio);
      } else if (windowHeight > maxHeight) {
        windowHeight = maxHeight;
        windowWidth = Math.floor(windowHeight * targetAspectRatio);
      }

      // 如果窗口当前是最大化状态，先取消最大化
      if (mainWindow.isMaximized()) {
        mainWindow.unmaximize();
      }

      // 设置窗口大小并居中
      mainWindow.setSize(windowWidth, windowHeight);
      mainWindow.center();

      // 清除保存的窗口状态，让下次启动使用默认大小
      if (store) {
        store.delete('windowState');
      }

      console.log('窗口已重置到默认大小:', { windowWidth, windowHeight });
      return true;
    }
    return false;
  });

  // 处理窗口关闭事件
  mainWindow.on('close', (event) => {
    const minimizeToTray = store.get('minimizeToTray', false);
    if (minimizeToTray && mainWindow) {
      event.preventDefault();
      mainWindow.hide();
      // 更新托盘菜单以反映窗口状态
      updateTrayMenu();
    }
  });
  
  // 获取语言设置
  ipcMain.handle('get-language', () => {
    return store.get('language', 'zh-CN');
  });
  
  // 设置语言
  ipcMain.handle('set-language', (_, language) => {
    currentLanguage = language;
    store.set('language', language);
    // 更新工作流引擎的语言设置
    if (workflowEngine) {
      workflowEngine.setLanguage(language);
    }
    updateTrayMenu(); // 更新托盘菜单语言
    return language;
  });

  // 获取开机自启动状态
  ipcMain.handle('auto-launch:get', () => {
    return store.get('autoLaunch', false);
  });

  // 设置开机自启动状态
  ipcMain.handle('auto-launch:set', (_, enable) => {
    store.set('autoLaunch', enable);
    setAutoLaunch(enable);
    updateTrayMenu(); // 更新托盘菜单
    return enable;
  });
  
  // 如果托盘不存在，创建托盘
  if (!tray) {
    createTray();
  }
}

// 更新托盘菜单
function updateTrayMenu() {
  if (!tray) return;
  
  const isVisible = mainWindow && mainWindow.isVisible();
  const lang = currentLanguage === 'zh-CN' ? 'zh' : 'en';
  const autoLaunch = store.get('autoLaunch', false);
  
  const menuTemplates: { [key: string]: Electron.MenuItemConstructorOptions[] } = {
    zh: [
      {
        label: isVisible ? '隐藏窗口' : '显示窗口',
        click: () => {
          if (mainWindow) {
            if (isVisible) {
              mainWindow.hide();
            } else {
              if (mainWindow.isMinimized()) {
                mainWindow.restore();
              }
              mainWindow.show();
              mainWindow.focus();
            }
            // 更新菜单以反映新的窗口状态
            updateTrayMenu();
          }
        }
      },
      {
        label: autoLaunch ? '禁用开机自启' : '启用开机自启',
        click: () => {
          const newValue = !autoLaunch;
          store.set('autoLaunch', newValue);
          setAutoLaunch(newValue);
          updateTrayMenu();
        }
      },
      {
        type: 'separator'
      },
      {
        label: '退出',
        click: () => {
          app.quit();
        }
      }
    ],
    en: [
      {
        label: isVisible ? 'Hide Window' : 'Show Window',
        click: () => {
          if (mainWindow) {
            if (isVisible) {
              mainWindow.hide();
            } else {
              if (mainWindow.isMinimized()) {
                mainWindow.restore();
              }
              mainWindow.show();
              mainWindow.focus();
            }
            // Update menu to reflect new window state
            updateTrayMenu();
          }
        }
      },
      {
        label: autoLaunch ? 'Disable Auto Launch' : 'Enable Auto Launch',
        click: () => {
          const newValue = !autoLaunch;
          store.set('autoLaunch', newValue);
          setAutoLaunch(newValue);
          updateTrayMenu();
        }
      },
      {
        type: 'separator'
      },
      {
        label: 'Exit',
        click: () => {
          app.quit();
        }
      }
    ]
  };
  
  const contextMenu = Menu.buildFromTemplate(menuTemplates[lang]);
  tray.setContextMenu(contextMenu);
}

// 设置开机自启动
function setAutoLaunch(enable: boolean) {
  if (process.env.NODE_ENV === 'development') {
    console.log('开发环境下不设置开机自启动');
    return;
  }

  try {
    app.setLoginItemSettings({
      openAtLogin: enable,
      // 在macOS上，可以设置为true以在Dock中隐藏应用程序
      openAsHidden: store.get('minimizeToTray', false),
      // 在Windows上，可以设置为true以在任务栏中隐藏应用程序
      args: store.get('minimizeToTray', false) ? ['--hidden'] : []
    });
    console.log(`开机自启动已${enable ? '启用' : '禁用'}`);
  } catch (error) {
    console.error('设置开机自启动失败:', error);
  }
}

// 创建系统托盘
function createTray() {
  // 如果托盘已存在，先销毁
  if (tray) {
    tray.destroy();
    tray = null;
  }

  // 创建托盘图标 - 修复打包后路径问题
  let iconPath: string;

  if (isDev) {
    // 开发环境
    iconPath = path.join(__dirname, '../../resources/logov1.ico');
  } else {
    // 生产环境 - 尝试多个可能的路径
    const possiblePaths = [
      path.join(process.resourcesPath, 'logov1.ico'),
      path.join(process.resourcesPath, 'resources', 'logov1.ico'),
      path.join(__dirname, '../resources/logov1.ico'),
      path.join(__dirname, '../../resources/logov1.ico')
    ];

    iconPath = possiblePaths.find(p => fs.existsSync(p)) || possiblePaths[0];
  }

  console.log('托盘图标路径:', iconPath, '存在:', fs.existsSync(iconPath));

  // 如果图标文件不存在，创建一个更好的简单图标
  if (!fs.existsSync(iconPath)) {
    console.log('图标文件不存在，使用内置图标');
    // 创建一个16x16的简单图标，使用更好的图标数据
    const icon = nativeImage.createFromDataURL(`data:image/png;base64,
iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAABFUlEQVQ4jZ2TMU7DQBBF3yyWHAMnQIrSpUFKQZMGCQkJCQkJCQmJC3ANTpAj0FBxABoKGqQUKVKkyFIsWd7/KbKWvdiOU/BLI828mf+1O7uzSkQwsy0z2zezQzMbBnvg7IOZjcxsambTRCQFfAQ+gTXQBSrAArhLdg/oAXPgHXgDnoCniIhFxCoiGhGZiMhcRJYiskrjIrIQkVlEpBGRRkSsAHaBc+AKuARGwAQYAw/AHfAKvAAvQXsP3AJT4BG4BobABXAGnAJHQA2UQAsoIiIxs7KqqpOiKM7zPD/Psuw0TdNBkiT9OI57URR1wzBsh2HYCsOwFQRBJwzDThzH3SRJ+lmWDfI8P6+q6sTMSuAb+AJ+gB9JvOQvLcUfBzjy8HMAAAAASUVORK5CYII=`);
    tray = new Tray(icon);
  } else {
    console.log('使用图标文件:', iconPath);
    tray = new Tray(iconPath);
  }

  // 设置托盘提示文本
  tray.setToolTip('ArroEngine - 文件整理工具');

  // 更新托盘菜单
  updateTrayMenu();

  // 双击托盘图标显示窗口
  tray.on('double-click', () => {
    if (mainWindow) {
      if (mainWindow.isMinimized()) {
        mainWindow.restore();
      }
      mainWindow.show();
      mainWindow.focus();
      // 更新托盘菜单以反映窗口状态
      updateTrayMenu();
    }
  });
  
  // 单击托盘图标切换窗口显示状态
  tray.on('click', () => {
    if (mainWindow) {
      if (mainWindow.isVisible()) {
        mainWindow.hide();
      } else {
        if (mainWindow.isMinimized()) {
          mainWindow.restore();
        }
        mainWindow.show();
        mainWindow.focus();
      }
      // 更新托盘菜单以反映窗口状态
      updateTrayMenu();
    }
  });
}

// Electron App 就绪后，创建窗口
app.whenReady().then(async () => {
  try {
    console.log('应用启动开始');

    // 动态导入electron-store，使用eval避免编译器转换
    const Store = (await eval('import("electron-store")')).default;
    store = new Store();

    // 获取当前语言设置
    currentLanguage = store.get('language', 'zh-CN');

    // 立即创建窗口，让用户看到界面
    console.log('创建主窗口');
    createWindow();

    // 延迟初始化工作流引擎和其他组件，避免阻塞界面
    // 使用更长的延迟确保界面完全加载
    setTimeout(async () => {
      try {
        console.log('开始初始化工作流引擎');
        // 初始化工作流引擎
        workflowEngine = new WorkflowEngine(currentLanguage, store);

        console.log('开始初始化监控管理器');
        // 初始化监控管理器
        monitorManager = new MonitorManager(workflowEngine, historyManager, store, async (workflowId: string) => {
          const workflows = await loadWorkflows();
          return workflows.find(w => w.id === workflowId) || null;
        });

        // 异步初始化监控管理器，不阻塞主线程
        monitorManager.initialize().then(() => {
          console.log('监控管理器初始化完成');
          // 设置监控事件转发
          setupMonitorEventForwarding();
        }).catch(error => {
          console.error('监控管理器初始化失败:', error);
        });

        // 预加载工作流，提升后续访问速度
        loadWorkflows().then(workflows => {
          console.log(`预加载工作流完成，数量: ${workflows.length}`);

          // 启动资源监控
          startResourceMonitoring();
        }).catch(error => {
          console.error('预加载工作流失败:', error);
        });

        // 初始化开机自启动设置
        const autoLaunch = store.get('autoLaunch', false);
        setAutoLaunch(autoLaunch);

        console.log('应用后台组件初始化完成');
      } catch (error) {
        console.error('后台组件初始化失败:', error);
      }
    }, 500); // 增加延迟到500ms，确保界面完全渲染

  } catch (error) {
    console.error('应用初始化失败:', error);
  }
});

// 当所有窗口关闭时退出应用 (macOS 除外)
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// 在 macOS 上，当点击 dock 图标并且没有其他窗口打开时，重新创建一个窗口
app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// 应用退出前清理所有资源
app.on('before-quit', async (event) => {
  console.log('应用准备退出，开始清理资源...');

  // 防止重复清理
  if (isQuitting) {
    return;
  }

  // 阻止默认退出行为，等待清理完成
  event.preventDefault();
  isQuitting = true;

  try {
    // 设置清理超时，防止清理过程卡住
    const cleanupTimeout = setTimeout(() => {
      console.warn('清理超时，强制退出');
      app.exit(1);
    }, 10000); // 10秒超时

    // 清理监控管理器
    if (monitorManager) {
      console.log('清理监控管理器...');
      await monitorManager.cleanup();
      monitorManager = null;
    }

    // 清理工作流引擎
    if (workflowEngine) {
      console.log('清理工作流引擎...');
      // 中断当前执行并保存部分结果
      workflowEngine.interrupt();

      // 获取当前执行状态
      const currentState = workflowEngine.getCurrentExecutionState();
      if (currentState) {
        console.log('检测到正在执行的工作流，尝试保存部分结果...');
        try {
          // 这里可以添加保存部分结果到历史记录的逻辑
          // 由于时间限制，暂时只记录日志
          console.log('部分执行状态:', {
            workflowId: currentState.workflowId,
            processedFiles: currentState.processedFiles,
            totalFiles: currentState.totalFiles,
            stepCount: currentState.stepResults.length
          });
        } catch (error) {
          console.error('保存部分执行结果失败:', error);
        }
      }

      workflowEngine = null;
    }

    // 清理工作流缓存
    console.log('清理工作流缓存...');
    clearWorkflowsCache();

    // 停止资源监控
    stopResourceMonitoring();

    // 销毁托盘
    if (tray) {
      console.log('销毁系统托盘...');
      tray.destroy();
      tray = null;
    }

    // 关闭主窗口
    if (mainWindow && !mainWindow.isDestroyed()) {
      console.log('关闭主窗口...');
      mainWindow.destroy();
      mainWindow = null;
    }

    // 清理所有 IPC 监听器
    console.log('清理 IPC 监听器...');
    ipcMain.removeAllListeners();

    // 清理所有定时器和间隔器
    console.log('清理定时器...');
    // 清理 Node.js 的活跃句柄（仅在开发环境中显示）
    if (isDev && (process as any)._getActiveHandles) {
      const handles = (process as any)._getActiveHandles();
      console.log(`活跃句柄数量: ${handles.length}`);
    }

    // 清理超时器
    clearTimeout(cleanupTimeout);

    console.log('资源清理完成，退出应用');

    // 强制退出
    app.exit(0);
  } catch (error) {
    console.error('清理资源时出错:', error);
    // 即使出错也要退出
    app.exit(1);
  }
});

// 处理进程信号，确保优雅退出
process.on('SIGINT', () => {
  console.log('收到 SIGINT 信号，准备退出...');
  if (!isQuitting) {
    app.quit();
  }
});

process.on('SIGTERM', () => {
  console.log('收到 SIGTERM 信号，准备退出...');
  if (!isQuitting) {
    app.quit();
  }
});

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
  if (!isQuitting) {
    app.quit();
  }
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的 Promise 拒绝:', reason, 'at:', promise);
});

// 工作流引擎实例
let workflowEngine: WorkflowEngine | null = null;

// 监控管理器实例
let monitorManager: MonitorManager | null = null;

// 资源监控定时器
let resourceMonitorTimer: NodeJS.Timeout | null = null;

// 启动资源监控
function startResourceMonitoring() {
  // 每5分钟检查一次资源使用情况
  resourceMonitorTimer = setInterval(() => {
    const memUsage = process.memoryUsage();
    const memUsageMB = {
      rss: Math.round(memUsage.rss / 1024 / 1024),
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
      external: Math.round(memUsage.external / 1024 / 1024)
    };

    console.log('内存使用情况 (MB):', memUsageMB);

    // 如果内存使用过高，发送警告
    if (memUsageMB.heapUsed > 500) { // 超过500MB
      console.warn('内存使用过高:', memUsageMB);
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('resource-warning', {
          type: 'memory',
          usage: memUsageMB
        });
      }
    }

    // 强制垃圾回收（仅在开发环境）
    if (isDev && global.gc) {
      global.gc();
      console.log('执行垃圾回收');
    }
  }, 5 * 60 * 1000); // 5分钟
}

// 停止资源监控
function stopResourceMonitoring() {
  if (resourceMonitorTimer) {
    clearInterval(resourceMonitorTimer);
    resourceMonitorTimer = null;
    console.log('资源监控已停止');
  }
}

// 工作流存储系统
const WORKFLOWS_FILE_PATH = path.join(app.getPath('userData'), 'workflows.json');

// 工作流缓存
let workflowsCache: Workflow[] | null = null;
let workflowsFileModTime = 0;

// 读取工作流（带智能缓存）
async function loadWorkflows(): Promise<Workflow[]> {
  try {
    // 检查文件是否存在
    if (await fs.pathExists(WORKFLOWS_FILE_PATH)) {
      // 获取文件修改时间
      const stats = await fs.stat(WORKFLOWS_FILE_PATH);
      const fileModTime = stats.mtime.getTime();

      console.log('文件修改时间检查:', {
        fileModTime: new Date(fileModTime).toISOString(),
        cachedModTime: workflowsFileModTime ? new Date(workflowsFileModTime).toISOString() : 'null',
        hasCachedData: !!workflowsCache,
        isFileModified: fileModTime !== workflowsFileModTime
      });

      // 如果缓存存在且文件没有修改，使用缓存
      if (workflowsCache && fileModTime === workflowsFileModTime) {
        console.log('使用缓存的工作流数据 (文件未修改)');
        return workflowsCache;
      }

      console.log('文件已修改或首次加载，重新读取工作流');
      workflowsFileModTime = fileModTime;
    }

    const startTime = performance.now();
    console.log('开始加载工作流，路径:', WORKFLOWS_FILE_PATH);

    if (await fs.pathExists(WORKFLOWS_FILE_PATH)) {
      const readStartTime = performance.now();
      const data = await fs.readFile(WORKFLOWS_FILE_PATH, 'utf-8');
      const readTime = performance.now() - readStartTime;
      console.log(`文件读取耗时: ${readTime.toFixed(2)}ms`);

      if (!data.trim()) {
        console.warn('工作流文件为空，初始化默认工作流');
        await initializeDefaultWorkflows('zh-CN');
        const defaultWorkflows = createDefaultWorkflows('zh-CN');

        // 更新缓存
        workflowsCache = defaultWorkflows;

        return defaultWorkflows;
      }

      const parseStartTime = performance.now();
      const existingWorkflows = JSON.parse(data);
      const parseTime = performance.now() - parseStartTime;
      console.log(`JSON解析耗时: ${parseTime.toFixed(2)}ms`);
      console.log('加载现有工作流，数量:', existingWorkflows.length);

      // 验证工作流数据完整性
      const validateStartTime = performance.now();
      const validWorkflows = existingWorkflows.filter((workflow: any) =>
        workflow && workflow.id && workflow.name
      );
      const validateTime = performance.now() - validateStartTime;
      console.log(`数据验证耗时: ${validateTime.toFixed(2)}ms`);

      if (validWorkflows.length !== existingWorkflows.length) {
        console.warn('发现损坏的工作流数据，已过滤:', existingWorkflows.length - validWorkflows.length, '个');
        await saveWorkflows(validWorkflows);
      }

      // 检查是否需要添加默认工作流
      const hasDefaultWorkflows = validWorkflows.some((workflow: any) => workflow.id.startsWith('workflow-'));
      if (!hasDefaultWorkflows) {
        console.log('未发现默认工作流，添加默认工作流');
        // 合并现有工作流和默认工作流
        const defaultWorkflows = createDefaultWorkflows('zh-CN');
        const mergedWorkflows = [...validWorkflows, ...defaultWorkflows];
        await saveWorkflows(mergedWorkflows);

        // 更新缓存
        workflowsCache = mergedWorkflows;

        return mergedWorkflows;
      }

      // 更新缓存
      workflowsCache = validWorkflows;

      const totalTime = performance.now() - startTime;
      console.log(`工作流加载总耗时: ${totalTime.toFixed(2)}ms`);

      return validWorkflows;
    }

    // 如果没有工作流文件，初始化默认工作流（使用中文作为默认语言）
    console.log('工作流文件不存在，初始化默认工作流');
    await initializeDefaultWorkflows('zh-CN');
    const defaultWorkflows = createDefaultWorkflows('zh-CN');

    // 更新缓存
    workflowsCache = defaultWorkflows;

    return defaultWorkflows;
  } catch (error) {
    console.error('加载工作流失败:', error);
    // 如果加载失败，返回默认工作流以确保应用能正常运行
    try {
      console.log('尝试返回默认工作流作为备用');
      const fallbackWorkflows = createDefaultWorkflows('zh-CN');

      // 更新缓存
      workflowsCache = fallbackWorkflows;

      return fallbackWorkflows;
    } catch (fallbackError) {
      console.error('创建默认工作流也失败:', fallbackError);
      return [];
    }
  }
}

// 清除工作流缓存
function clearWorkflowsCache() {
  console.log('清除工作流缓存');
  workflowsCache = null;
  workflowsFileModTime = 0;
}

// 初始化默认工作流
async function initializeDefaultWorkflows(language: 'zh-CN' | 'en-US' = 'zh-CN'): Promise<void> {
  try {
    const workflows = createDefaultWorkflows(language);
    await saveWorkflows(workflows);
    console.log('默认工作流已初始化, 语言:', language);
  } catch (error) {
    console.error('初始化默认工作流失败:', error);
  }
}

// 保存工作流
async function saveWorkflows(workflows: Workflow[]): Promise<void> {
  try {
    console.log('开始保存工作流，数量:', workflows.length);
    await fs.ensureDir(path.dirname(WORKFLOWS_FILE_PATH));

    // 验证工作流数据
    const validWorkflows: Workflow[] = [];
    const validationErrors: string[] = [];

    for (const workflow of workflows) {
      if (!workflow || !workflow.id || !workflow.name) {
        validationErrors.push(`工作流基本信息不完整: ${workflow?.name || '未知'}`);
        continue;
      }

      // 详细验证工作流配置
      const validation = validateWorkflow(workflow);
      if (validation.isValid) {
        validWorkflows.push(workflow);

        // 记录警告
        if (validation.warnings.length > 0) {
          console.warn(`工作流 "${workflow.name}" 存在警告:`, validation.warnings);
        }
      } else {
        validationErrors.push(`工作流 "${workflow.name}" 验证失败: ${validation.errors.join(', ')}`);
      }
    }

    if (validationErrors.length > 0) {
      console.warn('工作流验证错误:', validationErrors);
      // 发送验证错误到渲染进程
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('workflow-validation-errors', validationErrors);
      }
    }

    if (validWorkflows.length !== workflows.length) {
      console.warn('发现无效工作流数据，已过滤:', workflows.length - validWorkflows.length, '个');
    }

    await fs.writeFile(WORKFLOWS_FILE_PATH, JSON.stringify(validWorkflows, null, 2), 'utf-8');
    console.log('工作流保存成功，路径:', WORKFLOWS_FILE_PATH);

    // 清除缓存，确保下次加载最新数据
    clearWorkflowsCache();
  } catch (error) {
    console.error('保存工作流失败:', error);
    throw error;
  }
}



ipcMain.handle('dialog:openFile', async () => {
  // 只选择文件
  const { canceled, filePaths } = await dialog.showOpenDialog({
    properties: ['openFile', 'multiSelections', 'showHiddenFiles'],
    title: '选择文件',
    buttonLabel: '添加文件'
  });

  if (!canceled) {
    return filePaths;
  }

  return [];
});

// 递归获取文件夹中的所有文件和文件夹（保持文件夹内的排序顺序）
async function getAllItemsInDirectory(
  dirPath: string,
  maxItems?: number,
  includeSubfolders: boolean = true,
  processTarget: 'files' | 'folders' = 'files'
): Promise<string[]> {
  const items: string[] = [];

  async function processDirectory(currentPath: string, isRoot: boolean = false): Promise<void> {
    if (maxItems && items.length >= maxItems) {
      return;
    }

    try {
      const dirItems = await fs.readdir(currentPath);

      // 对文件夹内容进行排序，确保处理顺序一致
      const sortedItems = dirItems.sort((a, b) => {
        // 自然排序，处理数字序列
        return a.localeCompare(b, undefined, {
          numeric: true,
          sensitivity: 'base'
        });
      });

      // 顺序处理每个项目，不使用并发
      for (const item of sortedItems) {
        if (maxItems && items.length >= maxItems) {
          break;
        }

        const fullPath = path.join(currentPath, item);

        try {
          const stat = await fs.stat(fullPath);
          if (stat.isDirectory()) {
            // 根据处理目标决定是否添加文件夹
            if (processTarget === 'folders') {
              items.push(fullPath);
            }

            // 只有在包含子文件夹时才递归处理子目录
            if (includeSubfolders) {
              await processDirectory(fullPath, false);
            }
          } else {
            // 根据处理目标决定是否添加文件
            if (processTarget === 'files') {
              items.push(fullPath);
            }
          }
        } catch (error) {
          console.warn(`Error processing ${fullPath}:`, error);
        }
      }
    } catch (error) {
      console.error(`Error reading directory ${currentPath}:`, error);
    }
  }

  await processDirectory(dirPath, true);

  // 对最终结果再次排序，确保全局顺序一致
  return items.sort((a, b) => {
    return a.localeCompare(b, undefined, {
      numeric: true,
      sensitivity: 'base'
    });
  });
}

// 保持向后兼容的文件扫描函数
async function getAllFilesInDirectory(dirPath: string, maxFiles?: number, includeSubfolders: boolean = true): Promise<string[]> {
  return getAllItemsInDirectory(dirPath, maxFiles, includeSubfolders, 'files');
}

ipcMain.handle('dialog:openDirectory', async () => {
  const { canceled, filePaths } = await dialog.showOpenDialog({
    properties: ['openDirectory'],
    title: '选择文件夹',
    buttonLabel: '添加文件夹'
  });
  if (!canceled) {
    return filePaths[0]; // 返回选择的文件夹路径
  }
  return null;
});

// 统一的深度计算方法
function calculateDepth(itemPath: string, basePath?: string): number {
  const normalizedPath = path.normalize(itemPath);
  const parts = normalizedPath.split(path.sep).filter(part => part.length > 0);

  if (basePath) {
    const normalizedBasePath = path.normalize(basePath);
    const baseParts = normalizedBasePath.split(path.sep).filter(part => part.length > 0);
    return Math.max(0, parts.length - baseParts.length);
  }

  return parts.length;
}

// 检查文件夹是否逻辑上为空（包括只包含空子文件夹的情况）
async function isLogicallyEmpty(folderPath: string, visitedPaths: Set<string> = new Set()): Promise<boolean> {
  // 防止循环引用
  const realPath = await fs.realpath(folderPath).catch(() => folderPath);
  if (visitedPaths.has(realPath)) {
    return true; // 循环引用的情况视为空
  }

  visitedPaths.add(realPath);

  try {
    const items = await fs.readdir(folderPath);

    for (const item of items) {
      const itemPath = path.join(folderPath, item);
      try {
        const itemStat = await fs.lstat(itemPath);

        if (itemStat.isSymbolicLink()) {
          // 跳过符号链接
          continue;
        } else if (!itemStat.isDirectory()) {
          // 有文件，不为空
          visitedPaths.delete(realPath);
          return false;
        } else {
          // 递归检查子文件夹
          if (!(await isLogicallyEmpty(itemPath, visitedPaths))) {
            visitedPaths.delete(realPath);
            return false;
          }
        }
      } catch (error) {
        console.warn(`Error checking item ${itemPath}:`, error);
        // 出错的项目视为非空，保守处理
        visitedPaths.delete(realPath);
        return false;
      }
    }

    visitedPaths.delete(realPath);
    return true; // 所有子项都是空文件夹或不存在
  } catch (error) {
    console.warn(`Error reading directory ${folderPath}:`, error);
    visitedPaths.delete(realPath);
    return false; // 无法读取时视为非空
  }
}

// 递归计算文件夹大小的辅助函数
async function calculateFolderSize(folderPath: string, visitedPaths: Set<string> = new Set()): Promise<{ size: number, fileCount: number, folderCount: number }> {
  // 防止循环引用
  const realPath = await fs.realpath(folderPath).catch(() => folderPath);
  if (visitedPaths.has(realPath)) {
    console.warn(`Circular reference detected: ${folderPath}`);
    return { size: 0, fileCount: 0, folderCount: 0 };
  }

  visitedPaths.add(realPath);

  let totalSize = 0;
  let totalFileCount = 0;
  let totalFolderCount = 0;

  try {
    const items = await fs.readdir(folderPath);

    for (const item of items) {
      const itemPath = path.join(folderPath, item);
      try {
        const itemStat = await fs.lstat(itemPath); // 使用lstat避免跟随符号链接

        if (itemStat.isSymbolicLink()) {
          // 跳过符号链接，避免循环引用和重复计算
          continue;
        } else if (itemStat.isDirectory()) {
          totalFolderCount++;
          const subResult = await calculateFolderSize(itemPath, visitedPaths);
          totalSize += subResult.size;
          totalFileCount += subResult.fileCount;
          totalFolderCount += subResult.folderCount;
        } else {
          totalFileCount++;
          totalSize += itemStat.size;
        }
      } catch (error) {
        console.warn(`Error processing item ${itemPath}:`, error);
      }
    }
  } catch (error) {
    console.warn(`Error reading directory ${folderPath}:`, error);
  }

  visitedPaths.delete(realPath);
  return { size: totalSize, fileCount: totalFileCount, folderCount: totalFolderCount };
}

// 获取文件或文件夹的详细信息
async function getItemInfo(itemPath: string, originalDir?: string): Promise<AppFile> {
  const stat = await fs.lstat(itemPath); // 使用lstat获取符号链接信息
  const isDirectory = stat.isDirectory();
  const name = path.basename(itemPath);

  let fileCount = 0;
  let folderCount = 0;
  let totalSize = stat.size;
  let isEmpty = false;

  if (isDirectory) {
    try {
      const items = await fs.readdir(itemPath);
      const physicallyEmpty = items.length === 0;

      if (physicallyEmpty) {
        isEmpty = true;
      } else {
        // 递归计算文件夹大小和统计信息
        const folderStats = await calculateFolderSize(itemPath);
        totalSize = folderStats.size;
        fileCount = folderStats.fileCount;
        folderCount = folderStats.folderCount;

        // 检查是否逻辑上为空（只包含空子文件夹）
        isEmpty = await isLogicallyEmpty(itemPath);
      }
    } catch (error) {
      console.warn(`Error reading directory ${itemPath}:`, error);
    }
  }

  return {
    id: generateFileId(itemPath),
    name,
    path: itemPath,
    size: stat.size,
    type: isDirectory ? 'folder' : path.extname(name).substring(1) || 'file',
    status: 'pending',
    createdDate: stat.birthtime.toISOString(),
    modifiedDate: stat.mtime.toISOString(),
    isDirectory,
    originalDir: originalDir || (isDirectory ? itemPath : path.dirname(itemPath)), // 记录原始目录
    fileCount: isDirectory ? fileCount : undefined,
    folderCount: isDirectory ? folderCount : undefined,
    totalSize: isDirectory ? totalSize : undefined,
    isEmpty: isDirectory ? isEmpty : undefined,
    depth: calculateDepth(itemPath)
  };
}

// 处理拖拽的文件路径（包括文件夹）
ipcMain.handle('files:processDroppedPaths', async (_, paths: string[], workflowId?: string) => {
  const startTime = performance.now();
  const allItems: AppFile[] = [];
  // 从设置中获取处理上限，默认为1000
  const MAX_ITEMS = store.get('workflow.processing.maxItems', 1000) as number;

  console.log(`开始处理拖拽文件，路径数量: ${paths.length}, 工作流ID: ${workflowId}`);

  // 获取工作流设置以确定是否包含子文件夹和处理目标
  let includeSubfolders = true; // 默认包含子文件夹
  let processTarget: 'files' | 'folders' = 'files'; // 默认只处理文件

  if (workflowId) {
    try {
      const workflows = await loadWorkflows();
      const workflow = workflows.find(w => w.id === workflowId);
      if (workflow) {
        includeSubfolders = workflow.includeSubfolders !== false;
        // 如果工作流有步骤，使用第一个步骤的处理目标
        if (workflow.steps.length > 0) {
          processTarget = workflow.steps[0].processTarget || 'files';
        }
      }
    } catch (error) {
      console.warn('Failed to load workflow settings, using defaults', error);
    }
  }

  // 收集所有项目路径及其原始目录信息
  const itemsWithOriginalDir: Array<{path: string, originalDir: string}> = [];

  for (const itemPath of paths) {
    try {
      const stat = await fs.stat(itemPath);
      if (stat.isDirectory()) {
        // 如果是文件夹，根据工作流设置决定如何处理
        const itemsInDir = await getAllItemsInDirectory(itemPath, MAX_ITEMS - itemsWithOriginalDir.length, includeSubfolders, processTarget);
        // 为文件夹内的每个项目记录原始目录
        for (const subItemPath of itemsInDir) {
          itemsWithOriginalDir.push({
            path: subItemPath,
            originalDir: itemPath // 子项目的原始目录是被拖拽的文件夹
          });
        }

        // 如果已经达到最大项目数，停止处理
        if (itemsWithOriginalDir.length >= MAX_ITEMS) {
          break;
        }
      } else {
        // 如果是文件，根据处理目标决定是否添加
        if (processTarget === 'files') {
          itemsWithOriginalDir.push({
            path: itemPath,
            originalDir: path.dirname(itemPath) // 文件的原始目录是其父目录
          });
        }

        // 如果已经达到最大项目数，停止处理
        if (itemsWithOriginalDir.length >= MAX_ITEMS) {
          break;
        }
      }
    } catch (error) {
      console.error(`Error processing path ${itemPath}:`, error);
      // 发送错误通知到渲染进程
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('file-processing-error', {
          path: itemPath,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }
  }

  // 顺序处理项目信息，保持顺序
  for (const item of itemsWithOriginalDir) {
    try {
      const itemInfo = await getItemInfo(item.path, item.originalDir);
      allItems.push(itemInfo);
    } catch (error) {
      console.error(`Error getting item info for ${item.path}:`, error);
      // 返回一个基本的错误项目信息
      const parsedPath = path.parse(item.path);
      const errorItemInfo = {
        id: `item_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        name: parsedPath.base,
        path: item.path,
        size: 0,
        type: parsedPath.ext.slice(1).toLowerCase() || 'unknown',
        status: 'error' as const,
        error: 'Failed to get item info',
        createdDate: new Date().toISOString(),
        isDirectory: false,
        originalDir: item.originalDir
      };
      allItems.push(errorItemInfo);
    }
  }

  const totalTime = performance.now() - startTime;
  console.log(`文件处理完成，耗时: ${totalTime.toFixed(2)}ms, 处理文件数: ${allItems.length}`);

  // 如果处理时间过长，发送警告
  if (totalTime > 5000) { // 超过5秒
    console.warn(`文件处理耗时过长: ${totalTime.toFixed(2)}ms`);
    if (mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('performance-warning', {
        operation: 'file-processing',
        duration: totalTime,
        itemCount: allItems.length
      });
    }
  }

  return allItems;
});

// 在模块顶部导入crypto
const crypto = require('crypto');

// 生成统一的文件ID
function generateFileId(filePath: string): string {
  // 使用文件路径的哈希值确保同一文件始终有相同的ID
  const hash = crypto.createHash('md5').update(filePath).digest('hex');
  return `file_${hash.substring(0, 16)}`;
}

// 获取文件详细信息
async function getFileInfo(filePath: string): Promise<AppFile> {
  try {
    const stat = await fs.stat(filePath);
    const parsedPath = path.parse(filePath);

    return {
      id: generateFileId(filePath),
      name: parsedPath.base,
      path: filePath,
      size: stat.size,
      type: parsedPath.ext.slice(1).toLowerCase(),
      status: 'pending',
      createdDate: stat.birthtime.toISOString()
    };
  } catch (error) {
    console.error(`Error getting file info for ${filePath}:`, error);
    const parsedPath = path.parse(filePath);
    return {
      id: generateFileId(filePath),
      name: parsedPath.base,
      path: filePath,
      size: 0,
      type: parsedPath.ext.slice(1).toLowerCase(),
      status: 'error' as const,
      error: 'Failed to get file info',
      createdDate: new Date().toISOString()
    };
  }
}

// 工作流预览
ipcMain.handle('workflows:preview', async (_, files: AppFile[], workflow: Workflow) => {
  if (!workflowEngine) {
    throw new Error('工作流引擎未初始化');
  }
  return await workflowEngine.preview(files, workflow);
});

// 工作流执行
ipcMain.handle('workflows:execute', async (_, files: AppFile[], workflow: Workflow) => {
  if (!workflowEngine) {
    throw new Error('工作流引擎未初始化');
  }
  console.log('执行工作流:', workflow.name, '文件数量:', files.length);

  // 根据文件数量决定是否使用批处理
  const batchSize = store.get('workflow.processing.batchSize', 100) as number;
  const result = files.length > batchSize
    ? await workflowEngine.executeBatch(files, workflow, (progress) => {
        // 发送进度更新到渲染进程
        if (mainWindow && !mainWindow.isDestroyed()) {
          mainWindow.webContents.send('workflow-progress', progress);
        }
      })
    : await workflowEngine.execute(files, workflow);

  // 创建并添加历史记录（不影响主要功能）
  try {
    console.log('创建历史记录条目');
    // 在工作流执行完成后立即获取创建的文件夹信息，避免被清理逻辑清空
    const createdDirectories = workflowEngine.getAndPreserveCreatedDirectories();
    const cleanedEmptyDirectories = workflowEngine.getAndPreserveCleanedEmptyDirectories();
    console.log('📋 获取到的创建文件夹列表:', createdDirectories);
    console.log('📋 获取到的被清理空文件夹列表:', cleanedEmptyDirectories);

    const historyEntry = historyManager.createEntryFromWorkflowResult(
      result,
      workflow,
      files,
      'manual',
      undefined,
      undefined,
      createdDirectories,
      cleanedEmptyDirectories
    );
    await historyManager.addEntry(historyEntry);
    console.log('历史记录已保存，包含', createdDirectories.length, '个创建的文件夹和', cleanedEmptyDirectories.length, '个被清理的空文件夹');

    // 历史记录创建完成后，清空工作流引擎中的跟踪列表
    workflowEngine.clearCreatedDirectories();
    workflowEngine.clearCleanedEmptyDirectories();
  } catch (error) {
    console.error('Failed to save history entry:', error);
    // 不抛出错误，不影响工作流执行结果
  }

  return result;
});

// 工作流 CRUD 操作
// 1. 获取所有工作流
ipcMain.handle('workflows:getAll', async () => {
  return await loadWorkflows();
});

// 2. 保存工作流（新建或更新）
ipcMain.handle('workflows:save', async (_, workflow: Workflow) => {
  try {
    console.log('保存工作流:', workflow.id, workflow.name);
    const workflows = await loadWorkflows();
    const existingIndex = workflows.findIndex((w: Workflow) => w.id === workflow.id);

    if (existingIndex >= 0) {
      // 更新现有工作流
      workflows[existingIndex] = { ...workflow, updatedAt: new Date().toISOString() };
      console.log('更新现有工作流:', workflow.id);
    } else {
      // 添加新工作流
      const newWorkflow = { ...workflow, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() };
      workflows.push(newWorkflow);
      console.log('添加新工作流:', workflow.id);
    }

    await saveWorkflows(workflows);
    console.log('工作流保存成功，总数:', workflows.length);
    return workflow;
  } catch (error) {
    console.error('保存工作流失败:', error);
    throw error;
  }
});

// 3. 删除工作流
ipcMain.handle('workflows:delete', async (event, workflowId: string) => {
  const workflows = await loadWorkflows();
  const filteredWorkflows = workflows.filter((w: Workflow) => w.id !== workflowId);
  await saveWorkflows(filteredWorkflows);
  return true;
});

// 4. 获取单个工作流
ipcMain.handle('workflows:getById', async (event, workflowId: string) => {
  const workflows = await loadWorkflows();
  return workflows.find((w: Workflow) => w.id === workflowId) || null;
});

// 5. 重置默认工作流（保留用户创建的工作流）
ipcMain.handle('workflows:resetToDefault', async (_, language: 'zh-CN' | 'en-US' = 'zh-CN') => {
  try {
    const existingWorkflows = await loadWorkflows();
    const newDefaultWorkflows = createDefaultWorkflows(language);

    // 分离用户创建的工作流和默认工作流
    // 默认工作流的ID都以 'workflow-' 开头
    const userWorkflows = existingWorkflows.filter(w => !w.id.startsWith('workflow-'));

    // 合并用户创建的工作流和新的默认工作流
    // 先放用户工作流，再放默认工作流
    const combinedWorkflows = [...userWorkflows, ...newDefaultWorkflows];

    // 重新计算所有工作流的order，避免重复序号
    const allWorkflows = combinedWorkflows.map((workflow, index) => ({
      ...workflow,
      order: index + 1,
      updatedAt: new Date().toISOString()
    }));

    await saveWorkflows(allWorkflows);
    console.log('重置默认工作流成功, 语言:', language, '保留用户工作流:', userWorkflows.length, '个', '总工作流数:', allWorkflows.length);
    return true;
  } catch (error) {
    console.error('重置默认工作流失败:', error);
    return false;
  }
});

// 6. 更新默认工作流语言（保留用户自定义工作流）
ipcMain.handle('workflows:updateDefaultLanguage', async (event, language: 'zh-CN' | 'en-US' = 'zh-CN') => {
  try {
    const existingWorkflows = await loadWorkflows();
    const newDefaultWorkflows = createDefaultWorkflows(language);

    // 分离默认工作流和用户自定义工作流
    const userWorkflows = existingWorkflows.filter(w => !w.id.startsWith('workflow-'));
    const defaultWorkflowIds = new Set(newDefaultWorkflows.map(w => w.id));

    // 保留用户对默认工作流的修改（如启用/禁用状态、步骤配置等）
    const updatedDefaultWorkflows = newDefaultWorkflows.map(newWorkflow => {
      const existingWorkflow = existingWorkflows.find(w => w.id === newWorkflow.id);
      if (existingWorkflow) {
        // 更新工作流名称、描述，以及步骤的名称、描述、条件值和动作路径
        const updatedSteps = existingWorkflow.steps.map(existingStep => {
          const newStep = newWorkflow.steps.find(s => s.id === existingStep.id);
          if (newStep) {
            // 更新条件中的翻译值
            const updatedConditions = {
              ...existingStep.conditions,
              conditions: existingStep.conditions.conditions.map(existingCondition => {
                const newCondition = newStep.conditions.conditions.find(c => c.id === existingCondition.id);
                if (newCondition && existingCondition.field === 'fileType' && existingCondition.operator === 'equals') {
                  // 更新文件类型条件的值
                  return {
                    ...existingCondition,
                    value: newCondition.value
                  };
                }
                return existingCondition;
              })
            };

            // 更新动作中的目标路径
            const updatedActions = existingStep.actions.map(existingAction => {
              const newAction = newStep.actions.find(a => a.id === existingAction.id);
              if (newAction && newAction.config.targetPath) {
                return {
                  ...existingAction,
                  config: {
                    ...existingAction.config,
                    targetPath: newAction.config.targetPath
                  }
                };
              }
              return existingAction;
            });

            return {
              ...existingStep,
              name: newStep.name,
              description: newStep.description,
              conditions: updatedConditions,
              actions: updatedActions
            };
          }
          return existingStep;
        });

        return {
          ...existingWorkflow, // 保留用户的所有设置
          name: newWorkflow.name, // 更新工作流名称
          description: newWorkflow.description, // 更新工作流描述
          steps: updatedSteps, // 更新步骤的名称和描述
          updatedAt: new Date().toISOString()
        };
      }
      return newWorkflow;
    });

    // 合并用户自定义工作流和更新后的默认工作流
    // 先放用户工作流，再放默认工作流
    const allWorkflows = [...userWorkflows, ...updatedDefaultWorkflows];
    await saveWorkflows(allWorkflows);

    console.log('更新默认工作流语言成功, 语言:', language);
    return true;
  } catch (error) {
    console.error('更新默认工作流语言失败:', error);
    return false;
  }
});

// 历史记录 IPC 操作
// 1. 获取历史记录
ipcMain.handle('history:getAll', async (event, limit?: number, offset?: number) => {
  try {
    return await historyManager.getEntries(limit, offset);
  } catch (error) {
    console.error('Failed to get history entries:', error);
    return [];
  }
});

// 2. 搜索历史记录
ipcMain.handle('history:search', async (event, query: string, limit?: number) => {
  try {
    return await historyManager.searchEntries(query, limit);
  } catch (error) {
    console.error('Failed to search history entries:', error);
    return [];
  }
});

// 3. 清空历史记录
ipcMain.handle('history:clear', async () => {
  try {
    await historyManager.clearHistory();
    return true;
  } catch (error) {
    console.error('Failed to clear history:', error);
    return false;
  }
});

// 4. 删除单条历史记录
ipcMain.handle('history:delete', async (event, entryId: string) => {
  try {
    return await historyManager.deleteEntry(entryId);
  } catch (error) {
    console.error('Failed to delete history entry:', error);
    return false;
  }
});

// 5. 撤销历史记录操作
ipcMain.handle('history:undo', async (event, entryId: string) => {
  try {
    return await historyManager.undoEntry(entryId);
  } catch (error) {
    console.error('Failed to undo history entry:', error);
    return { success: false, message: '撤销操作失败' };
  }
});

// 6. 重做历史记录操作
ipcMain.handle('history:redo', async (event, entryId: string) => {
  try {
    return await historyManager.redoEntry(entryId);
  } catch (error) {
    console.error('Failed to redo history entry:', error);
    return { success: false, message: '重做操作失败' };
  }
});

// 7. 连锁撤回历史记录操作
ipcMain.handle('history:chainUndo', async (event, entryId: string) => {
  try {
    return await historyManager.chainUndoEntry(entryId);
  } catch (error) {
    console.error('Failed to chain undo history entry:', error);
    return { success: false, message: '连锁撤回操作失败' };
  }
});

// 7. 获取历史记录统计信息
ipcMain.handle('history:getStats', async () => {
  try {
    const historyEntries = await historyManager.getEntries();
    
    const totalEntries = historyEntries.length;
    const recentDays = new Set();
    const totalFiles = historyEntries.reduce((sum: number, entry: any) => sum + (entry.fileCount || 0), 0);
    const totalSuccessful = historyEntries.filter((entry: any) => entry.status === 'success').length;
    const totalPartial = historyEntries.filter((entry: any) => entry.status === 'partial').length;
    const totalFailed = historyEntries.filter((entry: any) => entry.status === 'error').length;
    
    // 计算最近操作的天数
    const now = new Date();
    historyEntries.forEach((entry: any) => {
      const entryDate = new Date(entry.timestamp);
      const dayDiff = Math.floor((now.getTime() - entryDate.getTime()) / (1000 * 60 * 60 * 24));
      if (dayDiff < 30) { // 只统计30天内的
        recentDays.add(Math.floor(entryDate.getTime() / (1000 * 60 * 60 * 24)));
      }
    });
    
    const activeDays = recentDays.size;
    
    return {
      totalEntries,
      totalFiles,
      totalSuccessful,
      totalPartial,
      totalFailed,
      activeDays,
      // 返回最近一条历史记录的时间
      lastActivity: totalEntries > 0 ? historyEntries[0].timestamp : null
    };
  } catch (error) {
    console.error('Failed to get history stats:', error);
    return {
      totalEntries: 0,
      totalFiles: 0,
      totalSuccessful: 0,
      totalPartial: 0,
      totalFailed: 0,
      activeDays: 0,
      lastActivity: null
    };
  }
});

// 8. 获取历史记录设置
ipcMain.handle('history:getSettings', async () => {
  try {
    return {
      maxEntries: store.get('history.maxEntries', 1000),
      autoCleanupDays: store.get('history.autoCleanupDays', 30)
    };
  } catch (error) {
    console.error('Failed to get history settings:', error);
    return {
      maxEntries: 1000,
      autoCleanupDays: 30
    };
  }
});

// 9. 更新历史记录设置
ipcMain.handle('history:updateSettings', async (event, settings: { maxEntries?: number; autoCleanupDays?: number }) => {
  try {
    if (settings.maxEntries !== undefined) {
      store.set('history.maxEntries', settings.maxEntries);
    }
    
    if (settings.autoCleanupDays !== undefined) {
      store.set('history.autoCleanupDays', settings.autoCleanupDays);
      
      // 如果启用了自动清理，执行一次清理操作
      const entries = await historyManager.getEntries();
      if (entries.length > 0 && settings.autoCleanupDays > 0) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - settings.autoCleanupDays);
        
        const oldEntries = entries.filter((entry: any) => new Date(entry.timestamp) < cutoffDate);
        for (const entry of oldEntries) {
          await historyManager.deleteEntry(entry.id);
        }
        
        console.log(`已清理 ${oldEntries.length} 条过期历史记录`);
      }
    }
    
    return true;
  } catch (error) {
    console.error('Failed to update history settings:', error);
    return false;
  }
});

// ==================== 监控功能 IPC 处理 ====================

// 1. 获取所有监控任务
ipcMain.handle('monitor:getAllTasks', async () => {
  try {
    if (!monitorManager) {
      throw new Error('监控管理器未初始化');
    }
    return monitorManager.getAllTasks();
  } catch (error) {
    console.error('Failed to get monitor tasks:', error);
    return [];
  }
});

// 2. 获取单个监控任务
ipcMain.handle('monitor:getTask', async (event, taskId: string) => {
  try {
    if (!monitorManager) {
      throw new Error('监控管理器未初始化');
    }
    return monitorManager.getTask(taskId);
  } catch (error) {
    console.error('Failed to get monitor task:', error);
    return null;
  }
});

// 3. 创建监控任务
ipcMain.handle('monitor:createTask', async (event, taskData: Omit<MonitorTask, 'id' | 'createdAt' | 'updatedAt' | 'statistics'>) => {
  try {
    if (!monitorManager) {
      throw new Error('监控管理器未初始化');
    }
    return await monitorManager.createTask(taskData);
  } catch (error) {
    console.error('Failed to create monitor task:', error);
    throw error;
  }
});

// 4. 更新监控任务
ipcMain.handle('monitor:updateTask', async (event, taskId: string, updates: Partial<MonitorTask>) => {
  try {
    if (!monitorManager) {
      throw new Error('监控管理器未初始化');
    }
    return await monitorManager.updateTask(taskId, updates);
  } catch (error) {
    console.error('Failed to update monitor task:', error);
    throw error;
  }
});

// 5. 删除监控任务
ipcMain.handle('monitor:deleteTask', async (event, taskId: string) => {
  try {
    if (!monitorManager) {
      throw new Error('监控管理器未初始化');
    }
    return await monitorManager.deleteTask(taskId);
  } catch (error) {
    console.error('Failed to delete monitor task:', error);
    return false;
  }
});

// 6. 手动执行监控任务
ipcMain.handle('monitor:executeTask', async (event, taskId: string, filePaths?: string[]) => {
  try {
    if (!monitorManager) {
      throw new Error('监控管理器未初始化');
    }
    return await monitorManager.executeTask(taskId, filePaths);
  } catch (error) {
    console.error('Failed to execute monitor task:', error);
    throw error;
  }
});

// 7. 获取任务状态
ipcMain.handle('monitor:getTaskStatus', async (event, taskId: string) => {
  try {
    if (!monitorManager) {
      throw new Error('监控管理器未初始化');
    }
    return monitorManager.getTaskStatus(taskId);
  } catch (error) {
    console.error('Failed to get task status:', error);
    return null;
  }
});

// 8. 获取所有任务状态
ipcMain.handle('monitor:getAllTaskStatuses', async () => {
  try {
    if (!monitorManager) {
      throw new Error('监控管理器未初始化');
    }
    return monitorManager.getAllTaskStatuses();
  } catch (error) {
    console.error('Failed to get all task statuses:', error);
    return [];
  }
});

// ==================== 设置功能 IPC 处理 ====================

// 获取设置
ipcMain.handle('settings:get', async (event, key: string) => {
  try {
    const value = store.get(key);
    console.log('获取设置:', key, '=', value);
    return value;
  } catch (error) {
    console.error('Failed to get setting:', error);
    return null;
  }
});

// 设置值
ipcMain.handle('settings:set', async (event, key: string, value: any) => {
  try {
    console.log('保存设置:', key, '=', value);
    store.set(key, value);

    // 验证设置是否保存成功
    const savedValue = store.get(key);
    console.log('设置保存后验证:', key, '=', savedValue);

    // 如果是minimizeToTray设置变化，更新托盘菜单
    if (key === 'minimizeToTray') {
      updateTrayMenu();
    }

    return true;
  } catch (error) {
    console.error('Failed to set setting:', error);
    return false;
  }
});

// 获取所有设置
ipcMain.handle('settings:getAll', async () => {
  try {
    return store.store;
  } catch (error) {
    console.error('Failed to get all settings:', error);
    return {};
  }
});

// ==================== 存储管理功能 ====================

// 获取存储使用情况
ipcMain.handle('storage:getUsage', async () => {
  try {
    const appDataPath = app.getPath('userData');
    
    // 获取应用数据大小
    const appDataSize = await getDirectorySize(appDataPath);
    
    // 获取历史数据大小
    const historyPath = path.join(app.getPath('userData'), 'history');
    let historySize = 0;
    
    if (await fs.pathExists(historyPath)) {
      historySize = await getDirectorySize(historyPath);
    }
    
    // 获取临时文件大小
    const tempPath = path.join(app.getPath('temp'), 'fileark-temp');
    let tempSize = 0;
    
    if (await fs.pathExists(tempPath)) {
      tempSize = await getDirectorySize(tempPath);
    }
    
    return {
      appDataSize,
      historySize,
      tempSize,
      totalSize: appDataSize
    };
  } catch (error) {
    console.error('Failed to get storage usage:', error);
    return {
      appDataSize: 0,
      historySize: 0,
      tempSize: 0,
      totalSize: 0
    };
  }
});

// 清理临时文件
ipcMain.handle('storage:cleanTemp', async () => {
  try {
    // 清理临时目录
    const tempPath = path.join(app.getPath('temp'), 'fileark-temp');
    
    if (await fs.pathExists(tempPath)) {
      await fs.emptyDir(tempPath);
    }
    
    return true;
  } catch (error) {
    console.error('Failed to clean temp files:', error);
    return false;
  }
});

// 获取目录大小的辅助函数
async function getDirectorySize(directoryPath: string): Promise<number> {
  try {
    if (!(await fs.pathExists(directoryPath))) {
      return 0;
    }
    
    const stats = await fs.stat(directoryPath);
    
    if (!stats.isDirectory()) {
      return stats.size;
    }
    
    const files = await fs.readdir(directoryPath);
    const sizes = await Promise.all(
      files.map(async (file) => {
        const filePath = path.join(directoryPath, file);
        const stats = await fs.stat(filePath);
        
        if (stats.isDirectory()) {
          return getDirectorySize(filePath);
        }
        
        return stats.size;
      })
    );
    
    return sizes.reduce((acc, size) => acc + size, 0);
  } catch (error) {
    console.error(`Error getting size for directory ${directoryPath}:`, error);
    return 0;
  }
}

// 监控事件转发到渲染进程的函数
const setupMonitorEventForwarding = () => {
  if (!monitorManager) return;

  monitorManager.on('taskCreated', (task) => {
    // 向所有窗口广播事件
    BrowserWindow.getAllWindows().forEach(window => {
      window.webContents.send('monitor:taskCreated', task);
    });
  });

  monitorManager.on('taskUpdated', (task) => {
    BrowserWindow.getAllWindows().forEach(window => {
      window.webContents.send('monitor:taskUpdated', task);
    });
  });

  monitorManager.on('taskDeleted', (taskId) => {
    BrowserWindow.getAllWindows().forEach(window => {
      window.webContents.send('monitor:taskDeleted', taskId);
    });
  });

  monitorManager.on('executionCompleted', (result) => {
    BrowserWindow.getAllWindows().forEach(window => {
      window.webContents.send('monitor:executionCompleted', result);
    });
  });

  monitorManager.on('executionFailed', (result) => {
    BrowserWindow.getAllWindows().forEach(window => {
      window.webContents.send('monitor:executionFailed', result);
    });
  });

  monitorManager.on('filesDetected', (data) => {
    BrowserWindow.getAllWindows().forEach(window => {
      window.webContents.send('monitor:filesDetected', data);
    });
  });

  monitorManager.on('taskError', (data) => {
    BrowserWindow.getAllWindows().forEach(window => {
      window.webContents.send('monitor:taskError', data);
    });
  });
}

// 注意：监控资源清理已合并到主要的 before-quit 事件处理器中

