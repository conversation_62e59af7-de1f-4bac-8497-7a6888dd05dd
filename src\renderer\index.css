@tailwind base;
@tailwind components;
@tailwind utilities;

/* 3D变换支持 */
@layer utilities {
  .perspective-1000 {
    perspective: 1000px;
  }

  .backface-hidden {
    backface-visibility: hidden;
  }

  .transform-gpu {
    transform: translateZ(0);
  }

  .rotate-y-0 {
    transform: rotateY(0deg);
  }

  .rotate-y-90 {
    transform: rotateY(90deg);
  }

  .rotate-y-\[-90deg\] {
    transform: rotateY(-90deg);
  }

  .rotate-y-\[90deg\] {
    transform: rotateY(90deg);
  }
}

/* 主题变量 */
:root {
  /* 浅色主题（默认） */
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;
  --bg-quaternary: #e5e7eb;
  --text-primary: #111827;
  --text-secondary: #374151;
  --text-tertiary: #6b7280;
  --border-primary: #e5e7eb;
  --border-secondary: #d1d5db;
  --scrollbar-track: #f3f4f6;
  --scrollbar-thumb: #d1d5db;
  --scrollbar-thumb-hover: #9ca3af;
}

/* 浅色主题 */
.light {
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;
  --bg-quaternary: #e5e7eb;
  --text-primary: #111827;
  --text-secondary: #374151;
  --text-tertiary: #6b7280;
  --border-primary: #e5e7eb;
  --border-secondary: #d1d5db;
  --scrollbar-track: #f3f4f6;
  --scrollbar-thumb: #d1d5db;
  --scrollbar-thumb-hover: #9ca3af;
}

/* 深色主题（显式） */
.dark {
  --bg-primary: #111827;
  --bg-secondary: #1f2937;
  --bg-tertiary: #374151;
  --bg-quaternary: #4b5563;
  --text-primary: #ffffff;
  --text-secondary: #d1d5db;
  --text-tertiary: #9ca3af;
  --border-primary: #374151;
  --border-secondary: #4b5563;
  --scrollbar-track: #374151;
  --scrollbar-thumb: #6b7280;
  --scrollbar-thumb-hover: #9ca3af;
}

/* 全局样式 */
html, body, #root {
  min-width: 1000px;
  min-height: 700px;
  overflow: hidden;
}

/* 自定义样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--scrollbar-track);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--scrollbar-thumb);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--scrollbar-thumb-hover);
}

/* 防止内容溢出 */
.overflow-hidden-x {
  overflow-x: hidden;
}

.overflow-hidden-y {
  overflow-y: hidden;
}

/* 隐藏滚动条但保留滚动功能 */
.scrollbar-hide {
  /* Firefox */
  scrollbar-width: none;
  /* Safari and Chrome */
  -ms-overflow-style: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* 增强渐变平滑度 */
.gradient-smooth {
  background-image: linear-gradient(135deg,
    rgba(31, 41, 55, 0.8) 0%,
    rgba(17, 24, 39, 0.75) 25%,
    rgba(31, 41, 55, 0.7) 50%,
    rgba(17, 24, 39, 0.65) 75%,
    rgba(31, 41, 55, 0.6) 100%);
  backdrop-filter: blur(8px) saturate(1.2);
  -webkit-backdrop-filter: blur(8px) saturate(1.2);
}

/* 为所有渐变背景添加抗锯齿 */
[class*="bg-gradient"] {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* 自定义标题栏样式 */
.custom-titlebar {
  -webkit-app-region: drag;
  user-select: none;
}

.custom-titlebar * {
  -webkit-app-region: no-drag;
}

/* 防止窗口控制按钮获得焦点时的高亮 */
button[tabindex="-1"]:focus {
  outline: none !important;
  box-shadow: none !important;
  background-color: transparent !important;
}

/* 防止页面加载时自动聚焦到第一个元素 */
*:focus {
  outline: none;
}

/* 只在键盘导航时显示焦点样式 */
.js-focus-visible :focus:not(.focus-visible) {
  outline: none;
}

/* 确保在不同操作系统上的兼容性 */
@media (prefers-color-scheme: dark) {
  .custom-titlebar {
    background-color: #1f2937;
    border-bottom: 1px solid #374151;
  }
}

/* Toggle 开关样式 */
.toggle {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
  background-color: #374151;
  border-radius: 12px;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
  appearance: none;
  outline: none;
}

.toggle:checked {
  background-color: #3b82f6;
}

.toggle::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background-color: white;
  border-radius: 50%;
  transition: transform 0.2s;
}

.toggle:checked::before {
  transform: translateX(20px);
}